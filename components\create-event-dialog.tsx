"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, MapPin, Users, Clock } from "lucide-react"

interface CreateEventDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit?: (eventData: any) => void
}

export function CreateEventDialog({ open, onOpenChange, onSubmit }: CreateEventDialogProps) {
  const [eventData, setEventData] = useState({
    name: "",
    type: "",
    date: "",
    time: "",
    venue: "",
    address: "",
    expectedGuests: "",
    description: "",
    planner: "",
    region: "",
  })
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (onSubmit) {
        await onSubmit(eventData)
      }

      // Reset form
      setEventData({
        name: "",
        type: "",
        date: "",
        time: "",
        venue: "",
        address: "",
        expectedGuests: "",
        description: "",
        planner: "",
        region: "",
      })
    } catch (error) {
      console.error("Error creating event:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Event</DialogTitle>
          <DialogDescription>Set up a new event with all the essential details to get started.</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Event Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Ngounou & Otantik Wedding"
                value={eventData.name}
                onChange={(e) => setEventData({ ...eventData, name: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Event Type *</Label>
              <Select value={eventData.type} onValueChange={(value) => setEventData({ ...eventData, type: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select event type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wedding">Wedding</SelectItem>
                  <SelectItem value="conference">Conference</SelectItem>
                  <SelectItem value="party">Party</SelectItem>
                  <SelectItem value="corporate">Corporate Event</SelectItem>
                  <SelectItem value="gala">Gala</SelectItem>
                  <SelectItem value="traditional">Traditional Ceremony</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Date *</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="date"
                  type="date"
                  value={eventData.date}
                  onChange={(e) => setEventData({ ...eventData, date: e.target.value })}
                  className="pl-10"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="time">Time *</Label>
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="time"
                  type="time"
                  value={eventData.time}
                  onChange={(e) => setEventData({ ...eventData, time: e.target.value })}
                  className="pl-10"
                  required
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="venue">Venue Name *</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="venue"
                placeholder="e.g., Hilton Hotel Yaoundé"
                value={eventData.venue}
                onChange={(e) => setEventData({ ...eventData, venue: e.target.value })}
                className="pl-10"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="address">Venue Address</Label>
              <Input
                id="address"
                placeholder="Full address"
                value={eventData.address}
                onChange={(e) => setEventData({ ...eventData, address: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="region">Region *</Label>
              <Select value={eventData.region} onValueChange={(value) => setEventData({ ...eventData, region: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="centre">Centre Region</SelectItem>
                  <SelectItem value="littoral">Littoral Region</SelectItem>
                  <SelectItem value="northwest">Northwest Region</SelectItem>
                  <SelectItem value="southwest">Southwest Region</SelectItem>
                  <SelectItem value="west">West Region</SelectItem>
                  <SelectItem value="east">East Region</SelectItem>
                  <SelectItem value="north">North Region</SelectItem>
                  <SelectItem value="adamawa">Adamawa Region</SelectItem>
                  <SelectItem value="far-north">Far North Region</SelectItem>
                  <SelectItem value="south">South Region</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="expectedGuests">Expected Guests</Label>
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="expectedGuests"
                  type="number"
                  placeholder="e.g., 150"
                  value={eventData.expectedGuests}
                  onChange={(e) => setEventData({ ...eventData, expectedGuests: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="planner">Assign Planner</Label>
              <Select
                value={eventData.planner}
                onValueChange={(value) => setEventData({ ...eventData, planner: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select planner" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bimm-audrey">Bimm Audrey</SelectItem>
                  <SelectItem value="tantoh-emmanuel">Tantoh Emmanuel</SelectItem>
                  <SelectItem value="ngounou-arlane">Ngounou Arlane</SelectItem>
                  <SelectItem value="otantik-nuna">Otantik Nuna</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Event Description</Label>
            <Textarea
              id="description"
              placeholder="Brief description of the event, special requirements, or notes..."
              value={eventData.description}
              onChange={(e) => setEventData({ ...eventData, description: e.target.value })}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" className="bg-gradient-to-r from-amber-500 to-yellow-500" disabled={loading}>
              {loading ? "Creating..." : "Create Event"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
