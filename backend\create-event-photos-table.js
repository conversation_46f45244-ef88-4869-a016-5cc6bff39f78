const mysql = require("mysql2/promise")

const dbConfig = {
  host: "localhost",
  user: "root",
  password: "",
  database: "otantik_ems"
}

async function createEventPhotosTable() {
  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(dbConfig)
    console.log("✅ Connected to database")

    // Create event_photos table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS event_photos (
        id INT PRIMARY KEY AUTO_INCREMENT,
        event_id INT NOT NULL,
        filename VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT,
        caption TEXT,
        uploaded_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREI<PERSON><PERSON> KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
        FOREIG<PERSON> KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
      )
    `)

    console.log("✅ Event photos table created successfully!")

    await connection.end()
  } catch (error) {
    console.error("❌ Error creating event photos table:", error.message)
  }
}

createEventPhotosTable() 