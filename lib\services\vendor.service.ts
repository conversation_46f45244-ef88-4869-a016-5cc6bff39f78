import apiClient from "@/lib/api"

export interface Vendor {
  id: number
  business_name: string
  contact_person: string
  email: string
  phone: string
  service_type: string
  region: string
  address: string
  description: string
  website: string
  rating: number
  total_events: number
  status: "active" | "inactive" | "pending"
  created_at: string
  updated_at: string
}

export interface CreateVendorData {
  business_name: string
  contact_person: string
  email: string
  phone: string
  service_type: string
  region: string
  address?: string
  description?: string
  website?: string
}

class VendorService {
  async getVendors(): Promise<Vendor[]> {
    return apiClient.request("/vendors")
  }

  async getVendor(id: number): Promise<Vendor> {
    return apiClient.request(`/vendors/${id}`)
  }

  async createVendor(vendorData: CreateVendorData): Promise<Vendor> {
    return apiClient.request("/vendors", {
      method: "POST",
      body: vendorData,
    })
  }

  async updateVendor(id: number, vendorData: Partial<Vendor>): Promise<Vendor> {
    return apiClient.request(`/vendors/${id}`, {
      method: "PUT",
      body: vendorData,
    })
  }

  async deleteVendor(id: number): Promise<void> {
    return apiClient.request(`/vendors/${id}`, {
      method: "DELETE",
    })
  }
}

export const vendorService = new VendorService()
