const mysql = require("mysql2/promise")
const bcrypt = require("bcryptjs")

const dbConfig = {
  host: "localhost",
  user: "root",
  password: "",
  database: "otantik_ems"
}

async function createTestUser() {
  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(dbConfig)
    console.log("✅ Connected to database")

    // Create test user
    const hashedPassword = await bcrypt.hash("password123", 10)
    
    const [result] = await connection.execute(`
      INSERT INTO users (first_name, last_name, email, password, role, region, phone) 
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, ["Test", "Planner", "<EMAIL>", hashedPassword, "planner", "centre", "1234567890"])

    console.log("✅ Test user created successfully!")
    console.log("📝 Login credentials:")
    console.log("   Email: <EMAIL>")
    console.log("   Password: password123")
    console.log("   Role: planner")

    await connection.end()
  } catch (error) {
    if (error.code === "ER_DUP_ENTRY") {
      console.log("⚠️  Test user already exists")
      console.log("📝 You can login with:")
      console.log("   Email: <EMAIL>")
      console.log("   Password: password123")
    } else {
      console.error("❌ Error creating test user:", error.message)
    }
  }
}

createTestUser() 