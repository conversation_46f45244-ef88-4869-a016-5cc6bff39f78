"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Users, Plus, Search, Filter, Mail, Phone, QrCode, Loader2, Edit, Trash2 } from "lucide-react"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export function GuestManagement() {
  const [guests, setGuests] = useState([])
  const [events, setEvents] = useState([])
  const [selectedEventId, setSelectedEventId] = useState("")
  const [searchTerm, setSearchTerm] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingGuest, setEditingGuest] = useState(null)
  const [newGuest, setNewGuest] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    table_number: "",
    seat_number: "",
    plus_one: false,
    region: "",
  })

  useEffect(() => {
    loadEvents()
  }, [])

  useEffect(() => {
    if (selectedEventId) {
      loadGuests()
    }
  }, [selectedEventId])

  const loadEvents = async () => {
    try {
      const eventsData = await apiClient.getEvents()
      setEvents(eventsData)
      if (eventsData.length > 0) {
        setSelectedEventId(eventsData[0].id.toString())
      }
    } catch (error) {
      console.error("Failed to load events:", error)
      toast.error("Failed to load events")
    }
  }

  const loadGuests = async () => {
    if (!selectedEventId) return

    try {
      setIsLoading(true)
      const guestsData = await apiClient.getGuests(Number.parseInt(selectedEventId))
      setGuests(guestsData)
    } catch (error) {
      console.error("Failed to load guests:", error)
      toast.error("Failed to load guests")
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddGuest = async () => {
    if (!selectedEventId) return

    try {
      const guestData = {
        ...newGuest,
        event_id: Number.parseInt(selectedEventId),
      }

      await apiClient.createGuest(guestData)
      toast.success("Guest added successfully")
      setShowAddDialog(false)
      setNewGuest({
        first_name: "",
        last_name: "",
        email: "",
        phone: "",
        table_number: "",
        seat_number: "",
        plus_one: false,
        region: "",
      })
      loadGuests()
    } catch (error) {
      toast.error("Failed to add guest")
    }
  }

  const handleEditGuest = async () => {
    if (!editingGuest) return

    try {
      await apiClient.updateGuest(editingGuest.id, editingGuest)
      toast.success("Guest updated successfully")
      setShowEditDialog(false)
      setEditingGuest(null)
      loadGuests()
    } catch (error) {
      toast.error("Failed to update guest")
    }
  }

  const handleDeleteGuest = async (guestId) => {
    if (!confirm("Are you sure you want to delete this guest?")) return

    try {
      await apiClient.deleteGuest(guestId)
      toast.success("Guest deleted successfully")
      loadGuests()
    } catch (error) {
      toast.error("Failed to delete guest")
    }
  }

  const handleGenerateQR = async (guestId) => {
    try {
      const qrCode = await apiClient.generateQRCode(guestId)
      // Here you would typically show the QR code in a modal or download it
      console.log("QR Code generated:", qrCode)
      toast.success("QR Code generated successfully")
    } catch (error) {
      toast.error("Failed to generate QR Code")
    }
  }

  const filteredGuests = guests.filter(
    (guest) =>
      `${guest.first_name} ${guest.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guest.email?.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const getStatusBadge = (status) => {
    switch (status) {
      case "confirmed":
        return <Badge className="bg-green-100 text-green-800">Confirmed</Badge>
      case "declined":
        return <Badge className="bg-red-100 text-red-800">Declined</Badge>
      case "pending":
        return <Badge variant="secondary">Pending</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              Guest Management
            </CardTitle>
            <CardDescription>Manage your event guest list</CardDescription>
          </div>
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600">
                <Plus className="h-4 w-4 mr-2" />
                Add Guest
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Add New Guest</DialogTitle>
                <DialogDescription>Add a new guest to the selected event</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={newGuest.first_name}
                      onChange={(e) => setNewGuest({ ...newGuest, first_name: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={newGuest.last_name}
                      onChange={(e) => setNewGuest({ ...newGuest, last_name: e.target.value })}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={newGuest.email}
                    onChange={(e) => setNewGuest({ ...newGuest, email: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={newGuest.phone}
                    onChange={(e) => setNewGuest({ ...newGuest, phone: e.target.value })}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="table">Table Number</Label>
                    <Input
                      id="table"
                      value={newGuest.table_number}
                      onChange={(e) => setNewGuest({ ...newGuest, table_number: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="seat">Seat Number</Label>
                    <Input
                      id="seat"
                      value={newGuest.seat_number}
                      onChange={(e) => setNewGuest({ ...newGuest, seat_number: e.target.value })}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="region">Region</Label>
                  <Input
                    id="region"
                    value={newGuest.region}
                    onChange={(e) => setNewGuest({ ...newGuest, region: e.target.value })}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddGuest}>Add Guest</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-4 mb-6">
          <div className="flex-1">
            <Select value={selectedEventId} onValueChange={setSelectedEventId}>
              <SelectTrigger>
                <SelectValue placeholder="Select an event" />
              </SelectTrigger>
              <SelectContent>
                {events.map((event) => (
                  <SelectItem key={event.id} value={event.id.toString()}>
                    {event.title} - {new Date(event.event_date).toLocaleDateString()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search guests..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading guests...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredGuests.map((guest) => (
              <div key={guest.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-4">
                  <Avatar>
                    <AvatarFallback>{`${guest.first_name?.[0] || ""}${guest.last_name?.[0] || ""}`}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{`${guest.first_name} ${guest.last_name}`}</h3>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {guest.email}
                      </div>
                      <div className="flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        {guest.phone}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      {getStatusBadge(guest.rsvp_status)}
                      {guest.table_number && (
                        <span className="text-xs text-gray-500">
                          Table {guest.table_number} - Seat {guest.seat_number}
                        </span>
                      )}
                      {guest.region && <span className="text-xs text-gray-500">{guest.region}</span>}
                      {guest.plus_one && (
                        <Badge variant="outline" className="text-xs">
                          +1
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={() => handleGenerateQR(guest.id)}>
                    <QrCode className="h-4 w-4 mr-1" />
                    QR Code
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setEditingGuest(guest)
                      setShowEditDialog(true)
                    }}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteGuest(guest.id)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            {filteredGuests.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No guests found</h3>
                <p className="text-gray-600">Add guests to this event to get started</p>
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Edit Guest Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Guest</DialogTitle>
            <DialogDescription>Update guest information</DialogDescription>
          </DialogHeader>
          {editingGuest && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="editFirstName">First Name</Label>
                  <Input
                    id="editFirstName"
                    value={editingGuest.first_name}
                    onChange={(e) => setEditingGuest({ ...editingGuest, first_name: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="editLastName">Last Name</Label>
                  <Input
                    id="editLastName"
                    value={editingGuest.last_name}
                    onChange={(e) => setEditingGuest({ ...editingGuest, last_name: e.target.value })}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="editEmail">Email</Label>
                <Input
                  id="editEmail"
                  type="email"
                  value={editingGuest.email}
                  onChange={(e) => setEditingGuest({ ...editingGuest, email: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="editPhone">Phone</Label>
                <Input
                  id="editPhone"
                  value={editingGuest.phone}
                  onChange={(e) => setEditingGuest({ ...editingGuest, phone: e.target.value })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="editTable">Table Number</Label>
                  <Input
                    id="editTable"
                    value={editingGuest.table_number}
                    onChange={(e) => setEditingGuest({ ...editingGuest, table_number: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="editSeat">Seat Number</Label>
                  <Input
                    id="editSeat"
                    value={editingGuest.seat_number}
                    onChange={(e) => setEditingGuest({ ...editingGuest, seat_number: e.target.value })}
                  />
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditGuest}>Update Guest</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
