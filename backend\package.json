{"name": "o<PERSON>ik-ems-backend", "version": "1.0.0", "description": "Backend for OtantikEMS - Event Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js", "setup": "npm run migrate && npm run seed"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["event-management", "nodejs", "express", "mysql"], "author": "OtantikEMS Team", "license": "MIT"}