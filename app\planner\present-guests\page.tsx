"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Loader2, Users, CheckCircle, Clock, Send, Search } from "lucide-react"
import apiClient from "@/lib/api"
import { toast } from "sonner"

import { PlannerSidebar } from "@/components/planner-sidebar"

interface PresentGuest {
  id: number
  guest_id: number
  event_id: number
  checkin_time: string
  checkin_method: string
  notes: string
  guest: {
    id: number
    first_name: string
    last_name: string
    email: string
    phone: string
    table_number?: number
  }
}

interface Event {
  id: number
  title: string
  event_date: string
}

export default function PresentGuestsPage() {
  const [events, setEvents] = useState<Event[]>([])
  const [selectedEventId, setSelectedEventId] = useState("")
  const [presentGuests, setPresentGuests] = useState<PresentGuest[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSendingMessages, setIsSendingMessages] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    loadEvents()
  }, [])

  useEffect(() => {
    if (selectedEventId) {
      loadPresentGuests()
    }
  }, [selectedEventId])

  const loadEvents = async () => {
    try {
      const eventsData = await apiClient.getEvents()
      setEvents(eventsData)
      if (eventsData.length > 0) {
        setSelectedEventId(eventsData[0].id.toString())
      }
    } catch (error) {
      console.error("Failed to load events:", error)
      toast.error("Failed to load events")
    } finally {
      setIsLoading(false)
    }
  }

  const loadPresentGuests = async () => {
    if (!selectedEventId) return

    try {
      const guests = await apiClient.getPresentGuests(parseInt(selectedEventId))
      setPresentGuests(guests)
    } catch (error) {
      console.error("Failed to load present guests:", error)
      toast.error("Failed to load present guests")
    }
  }

  const sendThankYouMessages = async () => {
    if (!selectedEventId) {
      toast.error("Please select an event")
      return
    }

    setIsSendingMessages(true)
    try {
      // Generate gallery link for the event
      let galleryLink = ""
      try {
        galleryLink = await apiClient.generateEventGalleryLink(parseInt(selectedEventId))
      } catch (error) {
        console.error("Failed to generate gallery link:", error)
        // Use fallback link
        galleryLink = `${window.location.origin}/gallery/${selectedEventId}`
      }

      // Create thank you message with photo link
      const thankYouMessage = `Dear {name},

Thank you so much for being part of our special day! Your presence made our celebration truly memorable.

We are grateful for the love, joy, and wonderful memories you helped create.

📸 Event Photos: ${galleryLink}

With heartfelt appreciation,
The Event Team

#OtantikEMS`

      const result = await apiClient.sendThankYouMessages(parseInt(selectedEventId), thankYouMessage)
      if (result.success) {
        toast.success(`Thank you messages with photo link sent to ${result.sentCount} guests!`)
      } else {
        toast.error(result.error || "Failed to send thank you messages")
      }
    } catch (error) {
      console.error("Failed to send thank you messages:", error)
      toast.error("Failed to send thank you messages")
    } finally {
      setIsSendingMessages(false)
    }
  }

  const filteredGuests = presentGuests.filter(guest => {
    const fullName = `${guest.guest.first_name} ${guest.guest.last_name}`.toLowerCase()
    const email = guest.guest.email.toLowerCase()
    const search = searchTerm.toLowerCase()
    return fullName.includes(search) || email.includes(search)
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading events...</span>
      </div>
    )
  }

  return (

    
    <div className="space-y-6">
        <div className="flex min-h-screen">
    {/* Sidebar */}
    <div className="w-64 border-r bg-white">
      <PlannerSidebar />
    </div>
    {/* Main Content */}
    <div className="flex-1 p-8 space-y-6">
    
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Present Guests</h1>
          <p className="text-gray-600 mt-2">Manage guests who have checked in to your event</p>
        </div>
        <Button
          onClick={sendThankYouMessages}
          disabled={isSendingMessages || presentGuests.length === 0}
          className="bg-gradient-to-r from-green-600 to-emerald-600"
        >
          {isSendingMessages ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Send className="h-4 w-4 mr-2" />
          )}
          Send Thank You Messages
        </Button>
      </div>

      {/* Event Selection */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Select value={selectedEventId} onValueChange={setSelectedEventId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select an event" />
                </SelectTrigger>
                <SelectContent>
                  {events.map((event) => (
                    <SelectItem key={event.id} value={event.id.toString()}>
                      {event.title} - {new Date(event.event_date).toLocaleDateString()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Present</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{presentGuests.length}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">QR Scans</p>
                <p className="text-3xl font-bold text-green-600 mt-2">
                  {presentGuests.filter(g => g.checkin_method === "qr_scan").length}
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Manual Check-ins</p>
                <p className="text-3xl font-bold text-amber-600 mt-2">
                  {presentGuests.filter(g => g.checkin_method === "manual_search").length}
                </p>
              </div>
              <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search guests by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Present Guests List */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Present Guests ({filteredGuests.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredGuests.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">
                {searchTerm ? "No guests found matching your search." : "No guests have checked in yet."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredGuests.map((guest) => (
                <div
                  key={guest.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {guest.guest.first_name} {guest.guest.last_name}
                        </h3>
                        <p className="text-sm text-gray-600">{guest.guest.email}</p>
                      </div>
                      <Badge variant={guest.checkin_method === "qr_scan" ? "default" : "secondary"}>
                        {guest.checkin_method === "qr_scan" ? "QR Scan" : "Manual"}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                      <span>Checked in: {new Date(guest.checkin_time).toLocaleString()}</span>
                      {guest.guest.table_number && (
                        <span>Table {guest.guest.table_number}</span>
                      )}
                      {guest.notes && <span>Notes: {guest.notes}</span>}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
    </div>
    </div>
  )
} 
