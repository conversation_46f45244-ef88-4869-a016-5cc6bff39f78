"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, UserCheck, Clock, Users, QrCode, CheckCircle, RefreshCw } from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"
import { toast } from "@/components/ui/use-toast"

interface Guest {
  id: number
  name: string
  email: string
  phone: string
  table_number: string
  seat_number: string
  rsvp_status: string
  checked_in: boolean
  check_in_time: string | null
  qr_code: string | null
}

export default function PlannerCheckin() {
  const [searchTerm, setSearchTerm] = useState("")
  const [qrScanMode, setQrScanMode] = useState(false)
  const [guests, setGuests] = useState<Guest[]>([])
  const [loading, setLoading] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const eventId = 1 // TODO: Replace with dynamic eventId

  const fetchGuests = useCallback(async () => {
    try {
      setLoading(true)
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests`, {
        credentials: "include",
      })
      if (!res.ok) throw new Error("Failed to fetch guests")
      const data = await res.json()
      setGuests(data)
      setLastUpdated(new Date())
    } catch (err) {
      console.error("Failed to fetch guests:", err)
      toast({
        title: "Error",
        description: "Failed to load guest data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }, [eventId])

  useEffect(() => {
    fetchGuests()
  }, [fetchGuests])

  // Auto-refresh every 30 seconds when enabled
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      fetchGuests()
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh, fetchGuests])

  const filteredGuests = guests.filter(
    (guest) =>
      guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (guest.email && guest.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (guest.phone && guest.phone.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (guest.table_number && guest.table_number.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const checkedInCount = guests.filter((g) => g.checked_in).length
  const totalGuests = guests.length
  const pendingCount = totalGuests - checkedInCount
  const attendanceRate = totalGuests === 0 ? 0 : Math.round((checkedInCount / totalGuests) * 100)

  const handleCheckIn = async (guestId: number) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests/${guestId}/checkin`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        // Update the guest in the local state
        setGuests(guests.map(guest =>
          guest.id === guestId
            ? { ...guest, checked_in: true, check_in_time: new Date().toISOString() }
            : guest
        ))
        toast({
          title: "Guest Checked In",
          description: "Guest has been successfully checked in"
        })
      } else {
        throw new Error('Failed to check in guest')
      }
    } catch (error) {
      toast({
        title: "Check-in Failed",
        description: "Failed to check in guest",
        variant: "destructive"
      })
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Live Check-in</h1>
              <p className="text-gray-600">Monitor guest arrivals in real-time</p>
              {lastUpdated && (
                <div className="flex items-center gap-2 mt-1">
                  <div className={`w-2 h-2 rounded-full ${autoRefresh ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
                  <span className="text-xs text-gray-500">
                    Last updated: {lastUpdated.toLocaleTimeString()}
                    {autoRefresh && ' • Auto-refresh enabled'}
                  </span>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={autoRefresh ? "default" : "outline"}
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                <Clock className="h-4 w-4 mr-2" />
                Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
              </Button>
              <Button variant={qrScanMode ? "default" : "outline"} onClick={() => setQrScanMode(!qrScanMode)}>
                <QrCode className="h-4 w-4 mr-2" />
                {qrScanMode ? "Exit QR Mode" : "QR Scanner"}
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Check-in Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Guests</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{totalGuests}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Checked In</p>
                    <p className="text-3xl font-bold text-green-600 mt-2">{checkedInCount}</p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-3xl font-bold text-amber-600 mt-2">{pendingCount}</p>
                  </div>
                  <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <Clock className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Attendance Rate</p>
                    <p className="text-3xl font-bold text-purple-600 mt-2">{attendanceRate}%</p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <UserCheck className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* QR Scanner Mode */}
          {qrScanMode && (
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5 text-blue-600" />
                  QR Code Scanner
                </CardTitle>
                <CardDescription>Scan guest QR codes for instant check-in</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-white rounded-lg border-2 border-dashed border-blue-300 p-8 text-center">
                  <QrCode className="h-16 w-16 mx-auto mb-4 text-blue-400" />
                  <p className="text-lg font-medium text-gray-700 mb-2">Ready to Scan</p>
                  <p className="text-gray-500">Position the QR code within the camera frame</p>
                  <div className="mt-4 h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                    <div className="border-2 border-blue-500 w-48 h-48 rounded-lg flex items-center justify-center">
                      <p className="text-blue-600 font-medium">Camera View</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Guest List */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <UserCheck className="h-5 w-5 text-green-600" />
                    Guest Check-in Status
                  </CardTitle>
                  <CardDescription>Monitor and manage guest arrivals</CardDescription>
                </div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search guests..."
                    className="pl-10 w-80"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredGuests.map((guest) => (
                  <div
                    key={guest.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center gap-4">
                      <Avatar>
                        <AvatarImage src={`/placeholder.svg?height=40&width=40`} />
                        <AvatarFallback>
                          {guest.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{guest.name}</h3>
                        <p className="text-sm text-gray-600">{guest.email || 'No email'}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline">{guest.table_number}</Badge>
                          {guest.seat_number && (
                            <Badge variant="outline" className="text-xs">
                              Seat {guest.seat_number}
                            </Badge>
                          )}
                          {guest.phone && (
                            <Badge variant="outline" className="text-xs">
                              {guest.phone}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      {guest.checked_in ? (
                        <div className="text-right">
                          <Badge className="bg-green-100 text-green-800 mb-1">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Checked In
                          </Badge>
                          {guest.check_in_time && (
                            <p className="text-xs text-gray-500">
                              at {new Date(guest.check_in_time).toLocaleTimeString()}
                            </p>
                          )}
                        </div>
                      ) : (
                        <div className="text-right">
                          <Badge variant="secondary" className="mb-1">
                            <Clock className="h-3 w-3 mr-1" />
                            Pending
                          </Badge>
                          <Button
                            size="sm"
                            className="bg-green-600 hover:bg-green-700"
                            onClick={() => handleCheckIn(guest.id)}
                            disabled={loading}
                          >
                            Check In
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
