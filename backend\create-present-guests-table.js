const mysql = require("mysql2/promise")

const dbConfig = {
  host: "localhost",
  user: "root",
  password: "",
  database: "otantik_ems"
}

async function createPresentGuestsTable() {
  try {
    console.log("🔌 Connecting to database...")
    const connection = await mysql.createConnection(dbConfig)
    console.log("✅ Connected to database")

    // Create present_guests table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS present_guests (
        id INT PRIMARY KEY AUTO_INCREMENT,
        event_id INT NOT NULL,
        guest_id INT NOT NULL,
        check_in_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        check_in_method ENUM('qr_scan', 'manual_search') NOT NULL,
        checked_by INT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
        FOREI<PERSON><PERSON> KEY (guest_id) REFERENCES guests(id) ON DELETE CASCADE,
        FOREIGN KEY (checked_by) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_event_guest (event_id, guest_id)
      )
    `)

    console.log("✅ Present guests table created successfully!")

    await connection.end()
  } catch (error) {
    console.error("❌ Error creating present guests table:", error.message)
  }
}

createPresentGuestsTable() 