const express = require("express")
const cors = require("cors")
const bcrypt = require("bcryptjs")
const jwt = require("jsonwebtoken")
require("dotenv").config()

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json())

// Mock data storage
let events = []
let users = [
  {
    id: 1,
    first_name: "Event",
    last_name: "Planner",
    email: "<EMAIL>",
    password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
    role: "planner",
    region: "centre",
    created_at: new Date().toISOString()
  }
]

let guests = []
let vendors = []

// JWT middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers["authorization"]
  const token = authHeader && authHeader.split(" ")[1]

  if (!token) {
    return res.status(401).json({ error: "Access token required" })
  }

  jwt.verify(token, process.env.JWT_SECRET || "your-secret-key", (err, user) => {
    if (err) {
      return res.status(403).json({ error: "Invalid token" })
    }
    req.user = user
    next()
  })
}

// Auth Routes
app.post("/api/auth/login", async (req, res) => {
  try {
    const { email, password } = req.body

    const user = users.find(u => u.email === email)

    if (!user) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const isValidPassword = await bcrypt.compare(password, user.password)

    if (!isValidPassword) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
      },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "24h" },
    )

    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
        region: user.region,
      },
    })
  } catch (error) {
    console.error("Login error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Events Routes
app.get("/api/events", authenticateToken, async (req, res) => {
  try {
    let filteredEvents = events

    if (req.user.role === "planner") {
      filteredEvents = events.filter(event => event.planner_id === req.user.id)
    }

    res.json(filteredEvents)
  } catch (error) {
    console.error("Get events error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/events", authenticateToken, async (req, res) => {
  try {
    const { title, description, event_date, event_time, venue, address, region, expected_guests, budget } = req.body

    const newEvent = {
      id: events.length + 1,
      title,
      description,
      event_date,
      event_time,
      venue,
      address,
      region,
      expected_guests: expected_guests || 0,
      budget: budget || 0,
      planner_id: req.user.id,
      status: "planning",
      invitation_template: "traditional",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    events.push(newEvent)

    res.status(201).json(newEvent)
  } catch (error) {
    console.error("Create event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.get("/api/events/:id", authenticateToken, async (req, res) => {
  try {
    const event = events.find(e => e.id === parseInt(req.params.id))

    if (!event) {
      return res.status(404).json({ error: "Event not found" })
    }

    res.json(event)
  } catch (error) {
    console.error("Get event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.put("/api/events/:id", authenticateToken, async (req, res) => {
  try {
    const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id))

    if (eventIndex === -1) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[eventIndex]
    
    // Only allow planners to update their own events, or admins to update any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Update event fields
    const updatedEvent = { ...event, ...req.body, updated_at: new Date().toISOString() }
    events[eventIndex] = updatedEvent

    res.json(updatedEvent)
  } catch (error) {
    console.error("Update event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.delete("/api/events/:id", authenticateToken, async (req, res) => {
  try {
    const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id))

    if (eventIndex === -1) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[eventIndex]
    
    // Only allow planners to delete their own events, or admins to delete any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Remove event
    events.splice(eventIndex, 1)

    res.json({ message: "Event deleted successfully" })
  } catch (error) {
    console.error("Delete event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Guests Routes
app.get("/api/events/:eventId/guests", authenticateToken, async (req, res) => {
  try {
    const eventGuests = guests.filter(g => g.event_id === parseInt(req.params.eventId))
    res.json(eventGuests)
  } catch (error) {
    console.error("Get guests error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/events/:eventId/guests", authenticateToken, async (req, res) => {
  try {
    const { name, email, phone, table_number, seat_number, dietary_requirements, plus_one } = req.body

    const newGuest = {
      id: guests.length + 1,
      event_id: parseInt(req.params.eventId),
      name,
      email,
      phone,
      table_number,
      seat_number,
      dietary_requirements,
      plus_one: plus_one || false,
      rsvp_status: "pending",
      checked_in: false,
      created_at: new Date().toISOString()
    }

    guests.push(newGuest)

    res.status(201).json(newGuest)
  } catch (error) {
    console.error("Create guest error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Dashboard Stats
app.get("/api/dashboard/stats", authenticateToken, async (req, res) => {
  try {
    let stats = {}

    if (req.user.role === "admin") {
      stats = {
        totalEvents: events.length,
        totalUsers: users.length,
        totalVendors: vendors.length,
        totalGuests: guests.length,
      }
    } else if (req.user.role === "planner") {
      const plannerEvents = events.filter(e => e.planner_id === req.user.id)
      const plannerGuests = guests.filter(g => 
        plannerEvents.some(e => e.id === g.event_id)
      )

      stats = {
        myEvents: plannerEvents.length,
        totalGuests: plannerGuests.length,
      }
    }

    res.json(stats)
  } catch (error) {
    console.error("Get dashboard stats error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Health check
app.get("/api/health", (req, res) => {
  res.json({ status: "OK", timestamp: new Date().toISOString() })
})

app.listen(PORT, () => {
  console.log(`Mock server running on port ${PORT}`)
  console.log(`Health check: http://localhost:${PORT}/api/health`)
}) 