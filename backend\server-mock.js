const express = require("express")
const cors = require("cors")
const bcrypt = require("bcryptjs")
const jwt = require("jsonwebtoken")
require("dotenv").config()

const app = express()
const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json())

// Mock data storage
let events = []
let users = [
  {
    id: 1,
    first_name: "Event",
    last_name: "Planner",
    email: "<EMAIL>",
    password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
    role: "planner",
    region: "centre",
    created_at: new Date().toISOString()
  }
]

let guests = []
let vendors = []

// JWT middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers["authorization"]
  const token = authHeader && authHeader.split(" ")[1]

  if (!token) {
    return res.status(401).json({ error: "Access token required" })
  }

  jwt.verify(token, process.env.JWT_SECRET || "your-secret-key", (err, user) => {
    if (err) {
      return res.status(403).json({ error: "Invalid token" })
    }
    req.user = user
    next()
  })
}

// Auth Routes
app.post("/api/auth/login", async (req, res) => {
  try {
    const { email, password } = req.body

    const user = users.find(u => u.email === email)

    if (!user) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const isValidPassword = await bcrypt.compare(password, user.password)

    if (!isValidPassword) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
      },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "24h" },
    )

    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
        region: user.region,
      },
    })
  } catch (error) {
    console.error("Login error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Events Routes
app.get("/api/events", authenticateToken, async (req, res) => {
  try {
    let filteredEvents = events

    if (req.user.role === "planner") {
      filteredEvents = events.filter(event => event.planner_id === req.user.id)
    }

    res.json(filteredEvents)
  } catch (error) {
    console.error("Get events error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/events", authenticateToken, async (req, res) => {
  try {
    const { title, description, event_date, event_time, venue, address, region, expected_guests, budget } = req.body

    const newEvent = {
      id: events.length + 1,
      title,
      description,
      event_date,
      event_time,
      venue,
      address,
      region,
      expected_guests: expected_guests || 0,
      budget: budget || 0,
      planner_id: req.user.id,
      status: "planning",
      invitation_template: "traditional",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    events.push(newEvent)

    res.status(201).json(newEvent)
  } catch (error) {
    console.error("Create event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.get("/api/events/:id", authenticateToken, async (req, res) => {
  try {
    const event = events.find(e => e.id === parseInt(req.params.id))

    if (!event) {
      return res.status(404).json({ error: "Event not found" })
    }

    res.json(event)
  } catch (error) {
    console.error("Get event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.put("/api/events/:id", authenticateToken, async (req, res) => {
  try {
    const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id))

    if (eventIndex === -1) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[eventIndex]
    
    // Only allow planners to update their own events, or admins to update any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Update event fields
    const updatedEvent = { ...event, ...req.body, updated_at: new Date().toISOString() }
    events[eventIndex] = updatedEvent

    res.json(updatedEvent)
  } catch (error) {
    console.error("Update event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.delete("/api/events/:id", authenticateToken, async (req, res) => {
  try {
    const eventIndex = events.findIndex(e => e.id === parseInt(req.params.id))

    if (eventIndex === -1) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[eventIndex]
    
    // Only allow planners to delete their own events, or admins to delete any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Remove event
    events.splice(eventIndex, 1)

    res.json({ message: "Event deleted successfully" })
  } catch (error) {
    console.error("Delete event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Guests Routes
app.get("/api/events/:eventId/guests", authenticateToken, async (req, res) => {
  try {
    const eventGuests = guests.filter(g => g.event_id === parseInt(req.params.eventId))
    res.json(eventGuests)
  } catch (error) {
    console.error("Get guests error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/events/:eventId/guests", authenticateToken, async (req, res) => {
  try {
    const { name, email, phone, table_number, seat_number, dietary_requirements, plus_one } = req.body

    const newGuest = {
      id: guests.length + 1,
      event_id: parseInt(req.params.eventId),
      name,
      email,
      phone,
      table_number,
      seat_number,
      dietary_requirements,
      plus_one: plus_one || false,
      rsvp_status: "pending",
      checked_in: false,
      created_at: new Date().toISOString()
    }

    guests.push(newGuest)

    res.status(201).json(newGuest)
  } catch (error) {
    console.error("Create guest error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Dashboard Stats
app.get("/api/dashboard/stats", authenticateToken, async (req, res) => {
  try {
    let stats = {}

    if (req.user.role === "admin") {
      stats = {
        totalEvents: events.length,
        totalUsers: users.length,
        totalVendors: vendors.length,
        totalGuests: guests.length,
      }
    } else if (req.user.role === "planner") {
      const plannerEvents = events.filter(e => e.planner_id === req.user.id)
      const plannerGuests = guests.filter(g => 
        plannerEvents.some(e => e.id === g.event_id)
      )

      stats = {
        myEvents: plannerEvents.length,
        totalGuests: plannerGuests.length,
      }
    }

    res.json(stats)
  } catch (error) {
    console.error("Get dashboard stats error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Mock financial settings storage
let financialSettings = [
  {
    id: 1,
    user_id: 1,
    default_profit_margin: 25.00,
    currency: 'XAF',
    tax_rate: 19.25,
    service_fee_percentage: 5.00,
    payment_terms: 'Net 30',
    invoice_prefix: 'BA-',
    auto_calculate_profit: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

// Mock notification settings storage
let notificationSettings = [
  {
    id: 1,
    user_id: 1,
    email_notifications: true,
    sms_notifications: false,
    push_notifications: true,
    guest_checkin_notifications: true,
    vendor_update_notifications: true,
    event_reminder_notifications: true,
    payment_notifications: true,
    system_notifications: true,
    marketing_notifications: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

// Mock event settings storage
let eventSettings = [
  {
    id: 1,
    user_id: 1,
    default_guest_limit: 500,
    auto_checkin_enabled: false,
    qr_code_expiry_hours: 24,
    allow_plus_ones: true,
    require_rsvp: true,
    default_invitation_template: 'traditional',
    auto_send_reminders: true,
    reminder_days_before: 7,
    auto_generate_qr_codes: true,
    enable_dietary_requirements: true,
    enable_seating_chart: false,
    default_event_duration_hours: 4,
    require_guest_phone: false,
    enable_guest_photos: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

let eventFinancialData = [
  {
    id: 1,
    event_id: 1,
    total_budget: 5000000.00,
    actual_cost: 3800000.00,
    profit_margin: 25.00,
    calculated_profit: 1200000.00,
    service_fee: 250000.00,
    tax_amount: 962500.00,
    final_amount: 6212500.00,
    payment_status: 'pending',
    invoice_number: 'BA-001',
    invoice_date: '2024-02-15',
    payment_due_date: '2024-03-15',
    notes: 'Wedding event financial data',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
]

// Financial Settings Routes

// Get financial settings for a user
app.get("/api/financial-settings", authenticateToken, async (req, res) => {
  try {
    const userSettings = financialSettings.find(s => s.user_id === req.user.id)

    if (!userSettings) {
      // Return default settings if none exist
      const defaultSettings = {
        user_id: req.user.id,
        default_profit_margin: 20.00,
        currency: 'XAF',
        tax_rate: 19.25,
        service_fee_percentage: 5.00,
        payment_terms: 'Net 30',
        invoice_prefix: 'INV',
        auto_calculate_profit: true
      }
      return res.json(defaultSettings)
    }

    res.json(userSettings)
  } catch (error) {
    console.error("Get financial settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update financial settings for a user
app.put("/api/financial-settings", authenticateToken, async (req, res) => {
  try {
    const {
      default_profit_margin,
      currency,
      tax_rate,
      service_fee_percentage,
      payment_terms,
      invoice_prefix,
      auto_calculate_profit
    } = req.body

    const existingIndex = financialSettings.findIndex(s => s.user_id === req.user.id)

    if (existingIndex === -1) {
      // Create new settings
      const newSettings = {
        id: financialSettings.length + 1,
        user_id: req.user.id,
        default_profit_margin,
        currency,
        tax_rate,
        service_fee_percentage,
        payment_terms,
        invoice_prefix,
        auto_calculate_profit,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      financialSettings.push(newSettings)
      res.json(newSettings)
    } else {
      // Update existing settings
      financialSettings[existingIndex] = {
        ...financialSettings[existingIndex],
        default_profit_margin,
        currency,
        tax_rate,
        service_fee_percentage,
        payment_terms,
        invoice_prefix,
        auto_calculate_profit,
        updated_at: new Date().toISOString()
      }

      res.json(financialSettings[existingIndex])
    }
  } catch (error) {
    console.error("Update financial settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Get event financial data
app.get("/api/events/:eventId/financial", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const eventIdNum = parseInt(eventId)

    // Verify event belongs to user
    const event = events.find(e => e.id === eventIdNum && e.planner_id === req.user.id)
    if (!event) {
      return res.status(404).json({ error: "Event not found" })
    }

    const financialData = eventFinancialData.find(f => f.event_id === eventIdNum)

    if (!financialData) {
      // Return default financial data if none exists
      const defaultData = {
        event_id: eventIdNum,
        total_budget: 0.00,
        actual_cost: 0.00,
        profit_margin: 0.00,
        calculated_profit: 0.00,
        service_fee: 0.00,
        tax_amount: 0.00,
        final_amount: 0.00,
        payment_status: 'pending'
      }
      return res.json(defaultData)
    }

    res.json(financialData)
  } catch (error) {
    console.error("Get event financial data error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update event financial data
app.put("/api/events/:eventId/financial", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const eventIdNum = parseInt(eventId)
    const {
      total_budget,
      actual_cost,
      profit_margin,
      calculated_profit,
      service_fee,
      tax_amount,
      final_amount,
      payment_status,
      invoice_number,
      invoice_date,
      payment_due_date,
      notes
    } = req.body

    // Verify event belongs to user
    const event = events.find(e => e.id === eventIdNum && e.planner_id === req.user.id)
    if (!event) {
      return res.status(404).json({ error: "Event not found" })
    }

    const existingIndex = eventFinancialData.findIndex(f => f.event_id === eventIdNum)

    if (existingIndex === -1) {
      // Create new financial data
      const newData = {
        id: eventFinancialData.length + 1,
        event_id: eventIdNum,
        total_budget,
        actual_cost,
        profit_margin,
        calculated_profit,
        service_fee,
        tax_amount,
        final_amount,
        payment_status,
        invoice_number,
        invoice_date,
        payment_due_date,
        notes,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      eventFinancialData.push(newData)
      res.json(newData)
    } else {
      // Update existing financial data
      eventFinancialData[existingIndex] = {
        ...eventFinancialData[existingIndex],
        total_budget,
        actual_cost,
        profit_margin,
        calculated_profit,
        service_fee,
        tax_amount,
        final_amount,
        payment_status,
        invoice_number,
        invoice_date,
        payment_due_date,
        notes,
        updated_at: new Date().toISOString()
      }

      res.json(eventFinancialData[existingIndex])
    }
  } catch (error) {
    console.error("Update event financial data error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Notification Settings Routes

// Get notification settings for a user
app.get("/api/notification-settings", authenticateToken, async (req, res) => {
  try {
    const userSettings = notificationSettings.find(s => s.user_id === req.user.id)

    if (!userSettings) {
      // Return default settings if none exist
      const defaultSettings = {
        user_id: req.user.id,
        email_notifications: true,
        sms_notifications: false,
        push_notifications: true,
        guest_checkin_notifications: true,
        vendor_update_notifications: true,
        event_reminder_notifications: true,
        payment_notifications: true,
        system_notifications: true,
        marketing_notifications: false
      }
      return res.json(defaultSettings)
    }

    res.json(userSettings)
  } catch (error) {
    console.error("Get notification settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update notification settings for a user
app.put("/api/notification-settings", authenticateToken, async (req, res) => {
  try {
    const {
      email_notifications,
      sms_notifications,
      push_notifications,
      guest_checkin_notifications,
      vendor_update_notifications,
      event_reminder_notifications,
      payment_notifications,
      system_notifications,
      marketing_notifications
    } = req.body

    const existingIndex = notificationSettings.findIndex(s => s.user_id === req.user.id)

    if (existingIndex === -1) {
      // Create new settings
      const newSettings = {
        id: notificationSettings.length + 1,
        user_id: req.user.id,
        email_notifications,
        sms_notifications,
        push_notifications,
        guest_checkin_notifications,
        vendor_update_notifications,
        event_reminder_notifications,
        payment_notifications,
        system_notifications,
        marketing_notifications,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      notificationSettings.push(newSettings)
      res.json(newSettings)
    } else {
      // Update existing settings
      notificationSettings[existingIndex] = {
        ...notificationSettings[existingIndex],
        email_notifications,
        sms_notifications,
        push_notifications,
        guest_checkin_notifications,
        vendor_update_notifications,
        event_reminder_notifications,
        payment_notifications,
        system_notifications,
        marketing_notifications,
        updated_at: new Date().toISOString()
      }

      res.json(notificationSettings[existingIndex])
    }
  } catch (error) {
    console.error("Update notification settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Event Settings Routes

// Get event settings for a user
app.get("/api/event-settings", authenticateToken, async (req, res) => {
  try {
    const userSettings = eventSettings.find(s => s.user_id === req.user.id)

    if (!userSettings) {
      // Return default settings if none exist
      const defaultSettings = {
        user_id: req.user.id,
        default_guest_limit: 500,
        auto_checkin_enabled: false,
        qr_code_expiry_hours: 24,
        allow_plus_ones: true,
        require_rsvp: true,
        default_invitation_template: 'traditional',
        auto_send_reminders: true,
        reminder_days_before: 7,
        auto_generate_qr_codes: true,
        enable_dietary_requirements: true,
        enable_seating_chart: false,
        default_event_duration_hours: 4,
        require_guest_phone: false,
        enable_guest_photos: true
      }
      return res.json(defaultSettings)
    }

    res.json(userSettings)
  } catch (error) {
    console.error("Get event settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update event settings for a user
app.put("/api/event-settings", authenticateToken, async (req, res) => {
  try {
    const {
      default_guest_limit,
      auto_checkin_enabled,
      qr_code_expiry_hours,
      allow_plus_ones,
      require_rsvp,
      default_invitation_template,
      auto_send_reminders,
      reminder_days_before,
      auto_generate_qr_codes,
      enable_dietary_requirements,
      enable_seating_chart,
      default_event_duration_hours,
      require_guest_phone,
      enable_guest_photos
    } = req.body

    const existingIndex = eventSettings.findIndex(s => s.user_id === req.user.id)

    if (existingIndex === -1) {
      // Create new settings
      const newSettings = {
        id: eventSettings.length + 1,
        user_id: req.user.id,
        default_guest_limit,
        auto_checkin_enabled,
        qr_code_expiry_hours,
        allow_plus_ones,
        require_rsvp,
        default_invitation_template,
        auto_send_reminders,
        reminder_days_before,
        auto_generate_qr_codes,
        enable_dietary_requirements,
        enable_seating_chart,
        default_event_duration_hours,
        require_guest_phone,
        enable_guest_photos,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      eventSettings.push(newSettings)
      res.json(newSettings)
    } else {
      // Update existing settings
      eventSettings[existingIndex] = {
        ...eventSettings[existingIndex],
        default_guest_limit,
        auto_checkin_enabled,
        qr_code_expiry_hours,
        allow_plus_ones,
        require_rsvp,
        default_invitation_template,
        auto_send_reminders,
        reminder_days_before,
        auto_generate_qr_codes,
        enable_dietary_requirements,
        enable_seating_chart,
        default_event_duration_hours,
        require_guest_phone,
        enable_guest_photos,
        updated_at: new Date().toISOString()
      }

      res.json(eventSettings[existingIndex])
    }
  } catch (error) {
    console.error("Update event settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Health check
app.get("/api/health", (req, res) => {
  res.json({ status: "OK", timestamp: new Date().toISOString() })
})

app.listen(PORT, () => {
  console.log(`Mock server running on port ${PORT}`)
  console.log(`Health check: http://localhost:${PORT}/api/health`)
}) 