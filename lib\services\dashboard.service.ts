import apiClient from "@/lib/api"
import { Event } from "./event.service"

export interface DashboardStats {
  totalEvents?: number
  totalUsers?: number
  totalVendors?: number
  totalGuests?: number
  myEvents?: number
  activeGuests?: number
  checkInRate?: number
  revenue?: number
}

export interface PlannerStats {
  totalEvents: number
  upcomingEvents: number
  completedEvents: number
  totalGuests: number
  recentEvents: any[]
  upcomingTasks: any[]
}

class DashboardService {
  async getStats(): Promise<DashboardStats> {
    return apiClient.request("/dashboard/stats")
  }

  async getPlannerStats(): Promise<PlannerStats> {
    try {
      const events: Event[] = await apiClient.request("/events")
      const currentDate = new Date()
      
      const totalEvents = events.length
      const upcomingEvents = events.filter((event: Event) => 
        new Date(event.event_date) > currentDate && event.status !== 'cancelled'
      ).length
      const completedEvents = events.filter((event: Event) => 
        event.status === 'completed'
      ).length
      
      // Calculate total guests across all events
      let totalGuests = 0
      for (const event of events) {
        try {
          const guests = await apiClient.request(`/events/${event.id}/guests`)
          totalGuests += guests.length
        } catch (error) {
          console.warn(`Could not fetch guests for event ${event.id}`)
        }
      }
      
      // Get recent events (last 5)
      const recentEvents = events
        .sort((a: Event, b: Event) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5)
        .map((event: Event) => ({
          name: event.title,
          date: new Date(event.event_date).toLocaleDateString(),
          status: event.status
        }))
      
      // Mock upcoming tasks
      const upcomingTasks = [
        {
          title: "Finalize guest list",
          event: "Ngounou & Otantik Wedding",
          dueDate: "2024-02-15",
          priority: "high"
        },
        {
          title: "Confirm venue details",
          event: "Corporate Conference",
          dueDate: "2024-02-20",
          priority: "medium"
        }
      ]
      
      return {
        totalEvents,
        upcomingEvents,
        completedEvents,
        totalGuests,
        recentEvents,
        upcomingTasks
      }
    } catch (error) {
      console.error("Error fetching planner stats:", error)
      // Return default values if API fails
      return {
        totalEvents: 0,
        upcomingEvents: 0,
        completedEvents: 0,
        totalGuests: 0,
        recentEvents: [],
        upcomingTasks: []
      }
    }
  }

  async getNotifications(): Promise<any[]> {
    return apiClient.request("/notifications")
  }

  async markNotificationAsRead(notificationId: number): Promise<void> {
    return apiClient.request(`/notifications/${notificationId}/read`, {
      method: "PUT",
    })
  }
}

export const dashboardService = new DashboardService()
