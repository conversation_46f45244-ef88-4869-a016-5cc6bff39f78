const express = require("express")
const cors = require("cors")
const mysql = require("mysql2/promise")
const bcrypt = require("bcryptjs")
const jwt = require("jsonwebtoken")
const multer = require("multer")
const path = require("path")
const fs = require("fs")

const csv = require('csv-parse');

const axios = require('axios');

require("dotenv").config()

const app = express()
const PORT = process.env.PORT || 5000

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002', 'http://localhost:3003'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}))
app.use(express.json())
app.use("/uploads", express.static("uploads"))

// Create uploads directory if it doesn't exist
if (!fs.existsSync("uploads")) {
  fs.mkdirSync("uploads")
}

// Database connection
const dbConfig = {
  host: "localhost",
  user:  "root",
  password: "",
  database: "otantik_ems",
  waitForConnections: true,

}

const pool = mysql.createPool(dbConfig)

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "uploads/")
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9)
    cb(null, file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname))
  },
})

const upload = multer({ storage })

// JWT middleware
const authenticateToken = (req, res, next) => {
  // For development/testing - bypass authentication
  if (process.env.NODE_ENV === 'development' || !process.env.JWT_SECRET) {
    req.user = { id: 1, role: 'planner' } // Mock user for testing
    return next()
  }

  const authHeader = req.headers["authorization"]
  const token = authHeader && authHeader.split(" ")[1]

  if (!token) {
    return res.status(401).json({ error: "Access token required" })
  }

  jwt.verify(token, process.env.JWT_SECRET || "your-secret-key", (err, user) => {
    if (err) {
      return res.status(403).json({ error: "Invalid token" })
    }
    req.user = user
    next()
  })
}


const Whatsapp_Token = 'EAAN9t6ZAg1okBPOPuZBHnM2dy8RyKcnNZCP84mRSSJJzms4ZBNTGl4ZBZBvxZC5yF99ASemsb9hoMANLZAZChlygw4LV83v71oOG6nhBn2IVB2kp9zw12kY9kJrvpVtRCv79Ye8YDg0WUi0aD2ZAhAKDqZBlCe66zZAZCyGC8cOZBhZCS8KZCCZAU6LlZCZCcIy03ZAuBCICRRcZClkeuJ7FpN3wq7vvrCnwZCanZBZAG5vUCBcDGtLZB6ZBUyZC0Ya';
const Access_Token = 'demotest';


app.get('/webhook', (req, res) => {
    const mode = req.query['hub.mode'];
    const challenge = req.query['hub.challenge'];
    const token = req.query['hub.verify_token'];
    if (mode && token === Access_Token) {
        res.status(200).send(challenge);
    } else {
        res.sendStatus(403);
    }
});

app.post('/webhook', (req, res) => {
    console.log(JSON.stringify(req.body, null, 2));
    res.status(200).send('webhook processed');
});

// Function to send text message
async function sendTextMessage(to, body) {
    await axios({
        url: 'https://graph.facebook.com/v22.0/616525048211153/messages',
        method: 'post',
        headers: {
            'Authorization': `Bearer ${Whatsapp_Token}`,
            'Content-Type': 'application/json',
        },
        data: {
            messaging_product: 'whatsapp',
            to,
            type: 'text',
            text: {
                body
            }
        }
    });
}

// Function to send image
async function sendImage(to, imageUrl) {
    await axios({
        url: 'https://graph.facebook.com/v22.0/616525048211153/messages',
        method: 'post',
        headers: {
            'Authorization': `Bearer ${Whatsapp_Token}`,
            'Content-Type': 'application/json',
        },
        data: {
            messaging_product: 'whatsapp',
            to,
            type: 'image',
            image: {
                link: imageUrl
            }
        }
    });
}


// Auth Routes
app.post("/api/auth/login", async (req, res) => {
  try {
    const { email, password } = req.body

    const [users] = await pool.execute("SELECT * FROM users WHERE email = ?", [email])

    if (users.length === 0) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const user = users[0]
    const isValidPassword = await bcrypt.compare(password, user.password)

    if (!isValidPassword) {
      return res.status(401).json({ error: "Invalid credentials" })
    }

    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
      },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "24h" },
    )

    res.json({
      token,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.first_name,
        lastName: user.last_name,
        region: user.region,
      },
    })
  } catch (error) {
    console.error("Login error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/auth/register", async (req, res) => {
  try {
    const { firstName, lastName, email, password, role, region, phone } = req.body

    // Check if user already exists
    const [existingUsers] = await pool.execute("SELECT id FROM users WHERE email = ?", [email])

    if (existingUsers.length > 0) {
      return res.status(400).json({ error: "User already exists" })
    }

    const hashedPassword = await bcrypt.hash(password, 10)

    const [result] = await pool.execute(
      "INSERT INTO users (first_name, last_name, email, password, role, region, phone) VALUES (?, ?, ?, ?, ?, ?, ?)",
      [firstName, lastName, email, hashedPassword, role, region, phone],
    )

    const token = jwt.sign(
      {
        id: result.insertId,
        email,
        role,
        firstName,
        lastName,
      },
      process.env.JWT_SECRET || "your-secret-key",
      { expiresIn: "24h" },
    )

    res.status(201).json({
      token,
      user: {
        id: result.insertId,
        email,
        role,
        firstName,
        lastName,
        region,
      },
    })
  } catch (error) {
    console.error("Registration error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Events Routes
app.get("/api/events", authenticateToken, async (req, res) => {
  try {
    let query = "SELECT * FROM events"
    const params = []

    if (req.user.role === "planner") {
      query += " WHERE planner_id = ?"
      params.push(req.user.id)
    }

    query += " ORDER BY event_date DESC"

    const [events] = await pool.execute(query, params)
    res.json(events)
  } catch (error) {
    console.error("Get events error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/events", authenticateToken, async (req, res) => {
  try {
    const { title, description, event_date, event_time, venue, address, region, expected_guests, budget } = req.body

    const [result] = await pool.execute(
      "INSERT INTO events (title, description, event_date, event_time, venue, address, region, expected_guests, budget, planner_id, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [
        title,
        description,
        event_date,
        event_time,
        venue,
        address,
        region,
        expected_guests,
        budget,
        req.user.id,
        "planning",
      ],
    )

    const [newEvent] = await pool.execute("SELECT * FROM events WHERE id = ?", [result.insertId])

    res.status(201).json(newEvent[0])
  } catch (error) {
    console.error("Create event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.get("/api/events/:id", authenticateToken, async (req, res) => {
  try {
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [req.params.id])

    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    res.json(events[0])
  } catch (error) {
    console.error("Get event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Guests Routes
app.get("/api/events/:eventId/guests", authenticateToken, async (req, res) => {
  try {
    const [guests] = await pool.execute(
      "SELECT * FROM guests WHERE event_id = ? ORDER BY table_number, seat_number, name",
      [req.params.eventId]
    )
    res.json(guests)
  } catch (error) {
    console.error("Get guests error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/events/:eventId/guests", authenticateToken, async (req, res) => {
  try {
    const { name, email, phone, table_number, seat_number } = req.body

    const [result] = await pool.execute(
      "INSERT INTO guests (event_id, name, email, phone, table_number, seat_number, rsvp_status) VALUES (?, ?, ?, ?, ?, ?, ?)",
      [req.params.eventId, name, email, phone, table_number, seat_number, "pending"],
    )

    const [newGuest] = await pool.execute("SELECT * FROM guests WHERE id = ?", [result.insertId])

    res.status(201).json(newGuest[0])
  } catch (error) {
    console.error("Create guest error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update guest
app.put("/api/events/:eventId/guests/:guestId", authenticateToken, async (req, res) => {
  try {
    const { name, email, phone, table_number, seat_number } = req.body
    const { eventId, guestId } = req.params

    const [result] = await pool.execute(
      `UPDATE guests SET name = ?, email = ?, phone = ?, table_number = ?, seat_number = ?,
       updated_at = CURRENT_TIMESTAMP WHERE id = ? AND event_id = ?`,
      [name, email, phone, table_number, seat_number, guestId, eventId]
    )

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: "Guest not found" })
    }

    const [updatedGuest] = await pool.execute("SELECT * FROM guests WHERE id = ?", [guestId])
    res.json(updatedGuest[0])
  } catch (error) {
    console.error("Update guest error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Delete guest
app.delete("/api/events/:eventId/guests/:guestId", authenticateToken, async (req, res) => {
  try {
    const { eventId, guestId } = req.params

    const [result] = await pool.execute(
      "DELETE FROM guests WHERE id = ? AND event_id = ?",
      [guestId, eventId]
    )

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: "Guest not found" })
    }

    res.json({ message: "Guest deleted successfully" })
  } catch (error) {
    console.error("Delete guest error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Check in guest
app.post("/api/events/:eventId/guests/:guestId/checkin", authenticateToken, async (req, res) => {
  try {
    const { eventId, guestId } = req.params

    const [result] = await pool.execute(
      "UPDATE guests SET checked_in = TRUE, check_in_time = CURRENT_TIMESTAMP WHERE id = ? AND event_id = ?",
      [guestId, eventId]
    )

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: "Guest not found" })
    }

    const [updatedGuest] = await pool.execute("SELECT * FROM guests WHERE id = ?", [guestId])
    res.json(updatedGuest[0])
  } catch (error) {
    console.error("Check in guest error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Save seating layout
app.post("/api/events/:eventId/seating-layout", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const { tables } = req.body

    // For now, we'll just return success since table positions can be stored in frontend
    // In a full implementation, you might want to store table layouts in the database

    res.json({
      success: true,
      message: "Seating layout saved successfully",
      tables: tables.length
    })
  } catch (error) {
    console.error("Save seating layout error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Test CSV parsing
app.post("/api/test-csv", upload.single("file"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    const csvData = req.file.buffer.toString();
    console.log('CSV data:', csvData);

    const { parse } = require('csv-parse/sync');
    const records = parse(csvData, {
      columns: true,
      trim: true,
      skip_empty_lines: true
    });

    console.log('Parsed records:', records);
    res.json({ success: true, records, count: records.length });
  } catch (error) {
    console.error('Test CSV error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Import guests from CSV
app.post("/api/events/:eventId/guests/import-csv", authenticateToken, upload.single("file"), async (req, res) => {
  console.log('CSV import request received');
  console.log('File:', req.file);
  console.log('Event ID:', req.params.eventId);

  if (!req.file) {
    console.log('No file uploaded');
    return res.status(400).json({ error: "No file uploaded" });
  }

  // Validate file type
  if (!req.file.originalname.toLowerCase().endsWith('.csv')) {
    console.log('Invalid file type:', req.file.originalname);
    return res.status(400).json({ error: "Only CSV files are allowed" });
  }

  console.log('File validation passed');

  const eventId = req.params.eventId;
  const results = [];
  const duplicates = [];
  const errors = [];
  const existingPhones = new Set();
  const existingEmails = new Set();
  const existingNamePhone = new Set();

  // Fetch existing guests for this event
  try {
    const [existingGuests] = await pool.execute("SELECT name, email, phone FROM guests WHERE event_id = ?", [eventId]);
    for (const guest of existingGuests) {
      if (guest.phone) existingPhones.add(guest.phone.trim());
      if (guest.email) existingEmails.add(guest.email.toLowerCase().trim());
      if (guest.name && guest.phone) {
        existingNamePhone.add(`${guest.name.toLowerCase().trim()}_${guest.phone.trim()}`);
      }
    }
  } catch (err) {
    return res.status(500).json({ error: "Failed to fetch existing guests" });
  }

  // Parse CSV
  try {
    console.log('Checking file data...');
    console.log('req.file.buffer exists:', !!req.file.buffer);
    console.log('req.file.path exists:', !!req.file.path);

    let csvData;
    if (req.file.buffer) {
      console.log('Using buffer data');
      csvData = req.file.buffer.toString();
    } else if (req.file.path) {
      console.log('Reading from file path:', req.file.path);
      const fs = require('fs');
      csvData = fs.readFileSync(req.file.path, 'utf8');
    } else {
      console.log('No file data available');
      return res.status(400).json({ error: "No file data received" });
    }

    console.log('Starting CSV parsing...');
    console.log('CSV data length:', csvData.length);
    console.log('First 200 chars:', csvData.substring(0, 200));

    // Parse CSV manually for better control
    console.log('About to parse CSV manually...');

    let records;
    try {
      const lines = csvData.split('\n').filter(line => line.trim());
      console.log('CSV lines found:', lines.length);

      if (lines.length === 0) {
        return res.status(400).json({ error: "CSV file is empty" });
      }

      // Get headers
      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      console.log('Headers found:', headers);

      // Parse data rows
      records = [];
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
        if (values.length === headers.length) {
          const record = {};
          headers.forEach((header, index) => {
            record[header] = values[index] || '';
          });
          records.push(record);
        }
      }

      console.log('CSV parsing successful! Records:', records.length);
      if (records.length > 0) {
        console.log('Sample record:', records[0]);
      }
    } catch (parseError) {
      console.error('CSV parse error:', parseError);
      return res.status(400).json({ error: "CSV parsing failed: " + parseError.message });
    }

    console.log('CSV parsing completed. Raw guests:', records.length);
    if (records.length > 0) {
      console.log('Sample guest data:', records[0]);
      console.log('Headers found:', Object.keys(records[0]));
    }

    // Validate and normalize guest data
    console.log('Starting guest validation and processing...');
    const processedGuests = [];

    for (let i = 0; i < records.length; i++) {
      const guest = records[i];
      const rowNumber = i + 2; // +2 because CSV starts at row 1 and we skip header
      console.log(`Processing guest ${i + 1}/${records.length}:`, guest);

        // Normalize field names (handle different possible column names)
        const normalizedGuest = {
          name: guest.name || guest.Name || guest.guest_name || guest['Guest Name'] || '',
          email: guest.email || guest.Email || guest.guest_email || guest['Guest Email'] || '',
          phone: guest.phone || guest.Phone || guest.guest_phone || guest['Guest Phone'] || '',
          table_number: guest.table_number || guest.table_seat || guest.table || guest.Table || guest['Table Number'] || guest['Table Seat'] || '',
          seat_number: guest.seat_number || guest.seat || guest.Seat || guest['Seat Number'] || ''
        };

        // Validate required fields
        if (!normalizedGuest.name.trim()) {
          errors.push({ row: rowNumber, field: 'name', message: 'Name is required' });
          continue;
        }

        if (!normalizedGuest.table_number.trim()) {
          errors.push({ row: rowNumber, field: 'table_number', message: 'Table number/seat is required' });
          continue;
        }

        // Validate email format if provided
        if (normalizedGuest.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(normalizedGuest.email)) {
          errors.push({ row: rowNumber, field: 'email', message: 'Invalid email format' });
          continue;
        }

        processedGuests.push({ ...normalizedGuest, rowNumber });
      }

      // Sort by table number/seat (handle both numeric and string sorting)
      processedGuests.sort((a, b) => {
        const tableA = a.table_number.toLowerCase();
        const tableB = b.table_number.toLowerCase();

        // Try to extract numbers for proper numeric sorting
        const numA = tableA.match(/\d+/);
        const numB = tableB.match(/\d+/);

        if (numA && numB) {
          const prefixA = tableA.replace(/\d+/, '');
          const prefixB = tableB.replace(/\d+/, '');

          if (prefixA === prefixB) {
            return parseInt(numA[0]) - parseInt(numB[0]);
          }
        }

        return tableA.localeCompare(tableB);
      });

      // Check for duplicates and insert
      const filePhones = new Set();
      const fileEmails = new Set();
      const fileNamePhone = new Set();

      for (const guest of processedGuests) {
        const namePhoneKey = `${guest.name.toLowerCase().trim()}_${guest.phone.trim()}`;
        const isDuplicateInDB = (guest.phone && existingPhones.has(guest.phone.trim())) ||
                               (guest.email && existingEmails.has(guest.email.toLowerCase().trim())) ||
                               existingNamePhone.has(namePhoneKey);

        const isDuplicateInFile = (guest.phone && filePhones.has(guest.phone.trim())) ||
                                 (guest.email && fileEmails.has(guest.email.toLowerCase().trim())) ||
                                 fileNamePhone.has(namePhoneKey);

        if (isDuplicateInDB) {
          duplicates.push({
            ...guest,
            reason: "Already exists in database",
            duplicate_type: "database"
          });
          continue;
        }

        if (isDuplicateInFile) {
          duplicates.push({
            ...guest,
            reason: "Duplicate within uploaded file",
            duplicate_type: "file"
          });
          continue;
        }

        // Track for file duplicates
        if (guest.phone) filePhones.add(guest.phone.trim());
        if (guest.email) fileEmails.add(guest.email.toLowerCase().trim());
        fileNamePhone.add(namePhoneKey);

        // Insert guest
        try {
          const [result] = await pool.execute(
            `INSERT INTO guests (event_id, name, email, phone, table_number, seat_number, rsvp_status)
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              eventId,
              guest.name.trim(),
              guest.email.trim() || null,
              guest.phone.trim() || null,
              guest.table_number.trim(),
              guest.seat_number.trim() || null,
              "pending"
            ]
          );

          results.push({
            ...guest,
            id: result.insertId,
            rsvp_status: "pending",
            checked_in: false
          });
        } catch (err) {
          duplicates.push({
            ...guest,
            reason: "Database error: " + err.message,
            duplicate_type: "error"
          });
        }
      }

      // Return all imported guests sorted by table
      try {
        const [allGuests] = await pool.execute(
          "SELECT * FROM guests WHERE event_id = ? ORDER BY table_number, seat_number",
          [eventId]
        );

        res.json({
          success: true,
          guests: allGuests,
          duplicates,
          errors,
          summary: {
            total_processed: records.length,
            successfully_imported: results.length,
            duplicates_found: duplicates.length,
            errors_found: errors.length
          }
        });
      } catch (err) {
        res.json({
          success: true,
          guests: results,
          duplicates,
          errors,
          summary: {
            total_processed: records.length,
            successfully_imported: results.length,
            duplicates_found: duplicates.length,
            errors_found: errors.length
          }
        });
      }

  } catch (error) {
    console.error("CSV import error:", error);
    res.status(500).json({ error: "Failed to process CSV file: " + error.message });
  }
});



// Check-in endpoint: Mark guest as checked in
app.post("/api/events/:eventId/guests/:guestId/checkin", authenticateToken, async (req, res) => {
  try {
    const { eventId, guestId } = req.params;
    
    const [result] = await pool.execute(
      "UPDATE guests SET checked_in = TRUE, check_in_time = NOW() WHERE id = ? AND event_id = ?",
      [guestId, eventId]
    );
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: "Guest not found" });
    }
   
    const [guests] = await pool.execute(
      "SELECT * FROM guests WHERE id = ? AND event_id = ?",
      [guestId, eventId]
    );
    res.json(guests[0]);
  } catch (error) {
    console.error("Check-in error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
});

// Search guests endpoint
app.get("/api/events/:eventId/guests/search", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { query } = req.query;
    if (!query || query.trim() === "") {
      return res.status(400).json({ error: "Query parameter is required" });
    }
    
    const [guests] = await pool.execute(
      `SELECT * FROM guests WHERE event_id = ? AND (
        LOWER(name) LIKE ? OR LOWER(email) LIKE ? OR phone LIKE ?
      ) ORDER BY created_at DESC`,
      [eventId, `%${query.toLowerCase()}%`, `%${query.toLowerCase()}%`, `%${query}%`]
    );
    res.json(guests);
  } catch (error) {
    console.error("Guest search error:", error);
    res.status(500).json({ error: "Internal server error" });

  }
});

// Vendors Routes
app.get("/api/vendors", authenticateToken, async (req, res) => {
  try {
    const [vendors] = await pool.execute("SELECT * FROM vendors ORDER BY created_at DESC")
    res.json(vendors)
  } catch (error) {
    console.error("Get vendors error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

app.post("/api/vendors", authenticateToken, async (req, res) => {
  try {
    const { business_name, contact_person, email, phone, service_type, region, address, description, website } =
      req.body

    const [result] = await pool.execute(
      "INSERT INTO vendors (business_name, contact_person, email, phone, service_type, region, address, description, website, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [business_name, contact_person, email, phone, service_type, region, address, description, website, "active"],
    )

    const [newVendor] = await pool.execute("SELECT * FROM vendors WHERE id = ?", [result.insertId])

    res.status(201).json(newVendor[0])
  } catch (error) {
    console.error("Create vendor error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Users Routes (Admin only)
app.get("/api/users", authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== "admin") {
      return res.status(403).json({ error: "Access denied" })
    }

    const [users] = await pool.execute(
      "SELECT id, first_name, last_name, email, role, region, phone, created_at FROM users ORDER BY created_at DESC",
    )
    res.json(users)
  } catch (error) {
    console.error("Get users error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Dashboard Stats
app.get("/api/dashboard/stats", authenticateToken, async (req, res) => {
  try {
    let stats = {}

    if (req.user.role === "admin") {
      const [eventCount] = await pool.execute("SELECT COUNT(*) as count FROM events")
      const [userCount] = await pool.execute("SELECT COUNT(*) as count FROM users")
      const [vendorCount] = await pool.execute("SELECT COUNT(*) as count FROM vendors")
      const [guestCount] = await pool.execute("SELECT COUNT(*) as count FROM guests")

      stats = {
        totalEvents: eventCount[0].count,
        totalUsers: userCount[0].count,
        totalVendors: vendorCount[0].count,
        totalGuests: guestCount[0].count,
      }
    } else if (req.user.role === "planner") {
      const [eventCount] = await pool.execute("SELECT COUNT(*) as count FROM events WHERE planner_id = ?", [
        req.user.id,
      ])
      const [guestCount] = await pool.execute(
        "SELECT COUNT(*) as count FROM guests g JOIN events e ON g.event_id = e.id WHERE e.planner_id = ?",
        [req.user.id],
      )

      stats = {
        myEvents: eventCount[0].count,
        totalGuests: guestCount[0].count,
      }
    }

    res.json(stats)
  } catch (error) {
    console.error("Get dashboard stats error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// File Upload
app.post("/api/upload", authenticateToken, upload.single("file"), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" })
    }

    res.json({
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      fileUrl: `/uploads/${req.file.filename}`,
    })
  } catch (error) {
    console.error("File upload error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})


// Event Photo Upload
app.post("/api/upload-event-photo", authenticateToken, upload.single("file"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" })
    }

    const { eventId, description } = req.body

    if (!eventId) {
      return res.status(400).json({ error: "Event ID is required" })
    }

    // Check if user has permission to upload photos for this event
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [eventId])
    
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[0]
    
    // Only allow planners to upload photos for their own events, or admins for any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Save photo record to database
    const [result] = await pool.execute(
      "INSERT INTO event_photos (event_id, filename, original_name, file_path, file_size, caption, uploaded_by) VALUES (?, ?, ?, ?, ?, ?, ?)",
      [
        eventId,
        req.file.filename,
        req.file.originalname,
        `/uploads/${req.file.filename}`,
        req.file.size,
        description || "",
        req.user.id,
      ]
    )

    const [newPhoto] = await pool.execute("SELECT * FROM event_photos WHERE id = ?", [result.insertId])

    res.json({
      id: newPhoto[0].id,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      fileUrl: `/uploads/${req.file.filename}`,
      caption: newPhoto[0].caption,
    })
  } catch (error) {
    console.error("Event photo upload error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})


app.get("/api/events/:id/public", async (req, res) => {
  try {
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [req.params.id])

    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    res.json(events[0])
  } catch (error) {
    console.error("Get event error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})


// Get Event Photos (Public - for gallery access)
app.get("/api/events/:eventId/photos", async (req, res) => {
  try {
    const eventId = req.params.eventId

    // Check if event exists
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [eventId])
    
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    const [photos] = await pool.execute(
      "SELECT * FROM event_photos WHERE event_id = ? ORDER BY created_at DESC",
      [eventId]
    )

    res.json(photos)
  } catch (error) {
    console.error("Get event photos error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Get Event Photos (Authenticated - for planner access)
app.get("/api/events/:eventId/photos/auth", authenticateToken, async (req, res) => {
  try {
    const eventId = req.params.eventId

    // Check if user has permission to view photos for this event
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [eventId])
    
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[0]
    
    // Only allow planners to view photos for their own events, or admins for any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    const [photos] = await pool.execute(
      "SELECT * FROM event_photos WHERE event_id = ? ORDER BY created_at DESC",
      [eventId]
    )

    res.json(photos)
  } catch (error) {
    console.error("Get event photos error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Delete Event Photo
app.delete("/api/photos/:photoId", authenticateToken, async (req, res) => {
  try {
    const photoId = req.params.photoId

    // Get photo details
    const [photos] = await pool.execute("SELECT * FROM event_photos WHERE id = ?", [photoId])
    
    if (photos.length === 0) {
      return res.status(404).json({ error: "Photo not found" })
    }

    const photo = photos[0]

    // Check if user has permission to delete this photo
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [photo.event_id])
    
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[0]
    
    // Only allow planners to delete photos for their own events, or admins for any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Delete photo file from filesystem
    const filePath = path.join(__dirname, "uploads", photo.filename)
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath)
    }

    // Delete photo record from database
    await pool.execute("DELETE FROM event_photos WHERE id = ?", [photoId])

    res.json({ message: "Photo deleted successfully" })
  } catch (error) {
    console.error("Delete photo error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update Event Photo
app.put("/api/photos/:photoId", authenticateToken, async (req, res) => {
  try {
    const photoId = req.params.photoId
    const { caption } = req.body

    // Get photo details
    const [photos] = await pool.execute("SELECT * FROM event_photos WHERE id = ?", [photoId])
    
    if (photos.length === 0) {
      return res.status(404).json({ error: "Photo not found" })
    }

    const photo = photos[0]

    // Check if user has permission to update this photo
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [photo.event_id])
    
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[0]
    
    // Only allow planners to update photos for their own events, or admins for any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Update photo record
    await pool.execute("UPDATE event_photos SET caption = ? WHERE id = ?", [caption, photoId])

    const [updatedPhoto] = await pool.execute("SELECT * FROM event_photos WHERE id = ?", [photoId])

    res.json(updatedPhoto[0])
  } catch (error) {
    console.error("Update photo error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Generate Event Gallery Link
app.get("/api/events/:eventId/gallery-link", authenticateToken, async (req, res) => {
  try {
    const eventId = req.params.eventId

    // Check if user has permission to access this event
    const [events] = await pool.execute("SELECT * FROM events WHERE id = ?", [eventId])
    
    if (events.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    const event = events[0]
    
    // Only allow planners to access their own events, or admins for any event
    if (req.user.role === "planner" && event.planner_id !== req.user.id) {
      return res.status(403).json({ error: "Access denied" })
    }

    // Generate public gallery link
    const galleryLink = `http://localhost:3000//gallery/${eventId}`

    res.json(galleryLink)
  } catch (error) {
    console.error("Generate gallery link error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})



app.post("/api/events/:eventId/send-invitations", authenticateToken, async (req, res) => {
  try {
    const { message, invitationImageUrl } = req.body

    const [guests] = await pool.execute("SELECT * FROM guests WHERE event_id = ? AND phone IS NOT NULL", [
      req.params.eventId,
    ])

   let successCount = 0;
let failCount = 0;
for (const guest of guests) {
  try {
    const personalizedMessage = `Dear ${guest.name},${message}`;
    const imageUrl = `https://8637-129-0-189-46.ngrok-free.app${invitationImageUrl}`;

    console.log(`Sending invitation to ${guest.name}${imageUrl}`);
    await sendTextMessage(`237${guest.phone}`, personalizedMessage);
    await sendImage(`237${guest.phone}`, `${imageUrl}`);

    // Update guest record to mark invitation as sent
    await pool.execute("UPDATE guests SET whatsapp_sent = TRUE, whatsapp_sent_at = NOW() WHERE id = ?", [guest.id]);

    successCount++;
  } catch (err) {
    console.error(`Failed to send to ${guest.name} (${guest.phone}):`, err.message);
    failCount++;
    // Optionally, update DB to mark as failed
  }

    }

    res.json({
      successCount,
      totalGuests: guests.length,
      message: "Invitations sent successfully",
    })
  } catch (error) {
    console.error("Send invitations error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})



app.post("/api/events/:eventId/send-thank-you", authenticateToken, async (req, res) => {
  try {
    const { message, invitationImageUrl } = req.body

    const [guests] = await pool.execute("SELECT * FROM guests WHERE event_id = ? AND phone IS NOT NULL AND checked_in = 1", [
      req.params.eventId,
    ])

   let successCount = 0;
let failCount = 0;
for (const guest of guests) {
  try {
    const personalizedMessage = `Dear ${guest.name},${message}`;
    console.log(`Sending to thank you message to ${guest.name}`);
    await sendTextMessage(`237${guest.phone}`, personalizedMessage);
  
    successCount++;
  } catch (err) {
    console.error(`Failed to send to ${guest.name} (${guest.phone}):`, err.message);
    failCount++;
    // Optionally, update DB to mark as failed
  }

    }

    res.json({
      successCount,
      totalGuests: guests.length,
      message: "Invitations sent successfully",
    })
  } catch (error) {
    console.error("Send invitations error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})


// Financial Settings Routes

// Get financial settings for a user
app.get("/api/financial-settings", authenticateToken, async (req, res) => {
  try {
    const [rows] = await pool.execute(
      "SELECT * FROM financial_settings WHERE user_id = ?",
      [req.user.id]
    )

    if (rows.length === 0) {
      // Return default settings if none exist
      const defaultSettings = {
        user_id: req.user.id,
        default_profit_margin: 20.00,
        currency: 'XAF',
        tax_rate: 19.25,
        service_fee_percentage: 5.00,
        payment_terms: 'Net 30',
        invoice_prefix: 'INV',
        auto_calculate_profit: true
      }
      return res.json(defaultSettings)
    }

    res.json(rows[0])
  } catch (error) {
    console.error("Get financial settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update financial settings for a user
app.put("/api/financial-settings", authenticateToken, async (req, res) => {
  try {
    const {
      default_profit_margin,
      currency,
      tax_rate,
      service_fee_percentage,
      payment_terms,
      invoice_prefix,
      auto_calculate_profit
    } = req.body

    // Check if settings exist
    const [existing] = await pool.execute(
      "SELECT id FROM financial_settings WHERE user_id = ?",
      [req.user.id]
    )

    if (existing.length === 0) {
      // Insert new settings
      const [result] = await pool.execute(
        `INSERT INTO financial_settings
         (user_id, default_profit_margin, currency, tax_rate, service_fee_percentage,
          payment_terms, invoice_prefix, auto_calculate_profit)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          req.user.id,
          default_profit_margin,
          currency,
          tax_rate,
          service_fee_percentage,
          payment_terms,
          invoice_prefix,
          auto_calculate_profit
        ]
      )

      const [newSettings] = await pool.execute(
        "SELECT * FROM financial_settings WHERE id = ?",
        [result.insertId]
      )

      res.json(newSettings[0])
    } else {
      // Update existing settings
      await pool.execute(
        `UPDATE financial_settings
         SET default_profit_margin = ?, currency = ?, tax_rate = ?,
             service_fee_percentage = ?, payment_terms = ?, invoice_prefix = ?,
             auto_calculate_profit = ?, updated_at = CURRENT_TIMESTAMP
         WHERE user_id = ?`,
        [
          default_profit_margin,
          currency,
          tax_rate,
          service_fee_percentage,
          payment_terms,
          invoice_prefix,
          auto_calculate_profit,
          req.user.id
        ]
      )

      const [updatedSettings] = await pool.execute(
        "SELECT * FROM financial_settings WHERE user_id = ?",
        [req.user.id]
      )

      res.json(updatedSettings[0])
    }
  } catch (error) {
    console.error("Update financial settings error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Get event financial data
app.get("/api/events/:eventId/financial", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params

    // Verify event belongs to user
    const [eventCheck] = await pool.execute(
      "SELECT id FROM events WHERE id = ? AND planner_id = ?",
      [eventId, req.user.id]
    )

    if (eventCheck.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    const [rows] = await pool.execute(
      "SELECT * FROM event_financial_data WHERE event_id = ?",
      [eventId]
    )

    if (rows.length === 0) {
      // Return default financial data if none exists
      const defaultData = {
        event_id: parseInt(eventId),
        total_budget: 0.00,
        actual_cost: 0.00,
        profit_margin: 0.00,
        calculated_profit: 0.00,
        service_fee: 0.00,
        tax_amount: 0.00,
        final_amount: 0.00,
        payment_status: 'pending'
      }
      return res.json(defaultData)
    }

    res.json(rows[0])
  } catch (error) {
    console.error("Get event financial data error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Update event financial data
app.put("/api/events/:eventId/financial", authenticateToken, async (req, res) => {
  try {
    const { eventId } = req.params
    const {
      total_budget,
      actual_cost,
      profit_margin,
      calculated_profit,
      service_fee,
      tax_amount,
      final_amount,
      payment_status,
      invoice_number,
      invoice_date,
      payment_due_date,
      notes
    } = req.body

    // Verify event belongs to user
    const [eventCheck] = await pool.execute(
      "SELECT id FROM events WHERE id = ? AND planner_id = ?",
      [eventId, req.user.id]
    )

    if (eventCheck.length === 0) {
      return res.status(404).json({ error: "Event not found" })
    }

    // Check if financial data exists
    const [existing] = await pool.execute(
      "SELECT id FROM event_financial_data WHERE event_id = ?",
      [eventId]
    )

    if (existing.length === 0) {
      // Insert new financial data
      const [result] = await pool.execute(
        `INSERT INTO event_financial_data
         (event_id, total_budget, actual_cost, profit_margin, calculated_profit,
          service_fee, tax_amount, final_amount, payment_status, invoice_number,
          invoice_date, payment_due_date, notes)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          eventId,
          total_budget,
          actual_cost,
          profit_margin,
          calculated_profit,
          service_fee,
          tax_amount,
          final_amount,
          payment_status,
          invoice_number,
          invoice_date,
          payment_due_date,
          notes
        ]
      )

      const [newData] = await pool.execute(
        "SELECT * FROM event_financial_data WHERE id = ?",
        [result.insertId]
      )

      res.json(newData[0])
    } else {
      // Update existing financial data
      await pool.execute(
        `UPDATE event_financial_data
         SET total_budget = ?, actual_cost = ?, profit_margin = ?,
             calculated_profit = ?, service_fee = ?, tax_amount = ?,
             final_amount = ?, payment_status = ?, invoice_number = ?,
             invoice_date = ?, payment_due_date = ?, notes = ?,
             updated_at = CURRENT_TIMESTAMP
         WHERE event_id = ?`,
        [
          total_budget,
          actual_cost,
          profit_margin,
          calculated_profit,
          service_fee,
          tax_amount,
          final_amount,
          payment_status,
          invoice_number,
          invoice_date,
          payment_due_date,
          notes,
          eventId
        ]
      )

      const [updatedData] = await pool.execute(
        "SELECT * FROM event_financial_data WHERE event_id = ?",
        [eventId]
      )

      res.json(updatedData[0])
    }
  } catch (error) {
    console.error("Update event financial data error:", error)
    res.status(500).json({ error: "Internal server error" })
  }
})

// Health check

app.get("/api/health", (req, res) => {
  res.json({ status: "OK", timestamp: new Date().toISOString() })
})



// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ error: "Something went wrong!" })
})

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`)

})
