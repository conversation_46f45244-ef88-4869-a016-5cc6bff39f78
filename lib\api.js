const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api"

class ApiClient {
  constructor() {
    this.token = null
    if (typeof window !== "undefined") {
      this.token = localStorage.getItem("token")
    }
  }

  setToken(token) {
    this.token = token
    if (typeof window !== "undefined") {
      if (token) {
        localStorage.setItem("token", token)
      } else {
        localStorage.removeItem("token")
      }
    }
  }

  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`
    const config = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    }

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`
    }

    if (config.body && typeof config.body === "object" && !(config.body instanceof FormData)) {
      config.body = JSON.stringify(config.body)
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Network error" }))
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
      }

      const contentType = response.headers.get("content-type")
      if (contentType && contentType.includes("application/json")) {
        return await response.json()
      }

      return response
    } catch (error) {
      console.error("API request failed:", error)
      throw error
    }
  }

  // Authentication
  async login(email, password) {
    const response = await this.request("/auth/login", {
      method: "POST",
      body: { email, password },
    })

    if (response.token) {
      this.setToken(response.token)
    }

    return response
  }

  async register(userData) {
    const response = await this.request("/auth/register", {
      method: "POST",
      body: userData,
    })

    if (response.token) {
      this.setToken(response.token)
    }

    return response
  }

  logout() {
    this.setToken(null)
  }

  // Events
  async getEvents() {
    return this.request("/events")
  }

  async createEvent(eventData) {
    return this.request("/events", {
      method: "POST",
      body: eventData,
    })
  }

  async getEvent(eventId) {
    return this.request(`/events/${eventId}`)
  }

  async updateEvent(eventId, eventData) {
    return this.request(`/events/${eventId}`, {
      method: "PUT",
      body: eventData,
    })
  }

  async deleteEvent(eventId) {
    return this.request(`/events/${eventId}`, {
      method: "DELETE",
    })
  }

  // Users (Planner only)
  async getUsers() {
    return this.request("/users")
  }

  async createUser(userData) {
    return this.request("/users", {
      method: "POST",
      body: userData,
    })
  }

  async updateUser(userId, userData) {
    return this.request(`/users/${userId}`, {
      method: "PUT",
      body: userData,
    })
  }

  async deleteUser(userId) {
    return this.request(`/users/${userId}`, {
      method: "DELETE",
    })
  }

  // Vendors
  async getVendors() {
    return this.request("/vendors")
  }

  async createVendor(vendorData) {
    return this.request("/vendors", {
      method: "POST",
      body: vendorData,
    })
  }

  async updateVendor(vendorId, vendorData) {
    return this.request(`/vendors/${vendorId}`, {
      method: "PUT",
      body: vendorData,
    })
  }

  async deleteVendor(vendorId) {
    return this.request(`/vendors/${vendorId}`, {
      method: "DELETE",
    })
  }

  // Guests
  async getGuests(eventId) {
    return this.request(`/events/${eventId}/guests`)
  }

  async createGuest(eventId, guestData) {
    return this.request(`/events/${eventId}/guests`, {
      method: "POST",
      body: guestData,
    })
  }

  async updateGuest(eventId, guestId, guestData) {
    return this.request(`/events/${eventId}/guests/${guestId}`, {
      method: "PUT",
      body: guestData,
    })
  }

  async deleteGuest(eventId, guestId) {
    return this.request(`/events/${eventId}/guests/${guestId}`, {
      method: "DELETE",
    })
  }

  // QR Codes
  async generateQRCode(eventId, guestId) {
    return this.request(`/events/${eventId}/guests/${guestId}/qr`)
  }

  // Check-in
  async checkInGuest(qrData) {
    return this.request("/checkin", {
      method: "POST",
      body: { qrData },
    })
  }

  // Present Guests Management
  async checkInGuestToPresentList(eventId, guestId, checkInMethod, notes) {
    return this.request(`/events/${eventId}/check-in`, {
      method: "POST",
      body: { guestId, checkInMethod, notes },
    })
  }

  async getPresentGuests(eventId) {
    return this.request(`/events/${eventId}/present-guests`)
  }

  async sendThankYouToPresentGuests(eventId, message) {
    return this.request(`/events/${eventId}/send-thank-you`, {
      method: "POST",
      body: { message },
    })
  }

  async sendThankYouMessages(eventId, message) {
    return this.request(`/events/${eventId}/send-thank-you`, {
      method: "POST",
      body: { message },
    })
  }

  // WhatsApp Integration
  async sendInvitations(eventId, message, invitationImageUrl = null) {
    return this.request(`/events/${eventId}/send-invitations`, {
      method: "POST",
      body: { message, invitationImageUrl },
    })
  }

  async sendThankYouMessages(eventId, message) {
    return this.request(`/events/${eventId}/send-thank-you`, {
      method: "POST",
      body: { message },
    })
  }

  // File Upload
  async uploadFile(file, eventId = null, description = "") {
    const formData = new FormData()
    formData.append("file", file)
    if (eventId) formData.append("eventId", eventId)
    if (description) formData.append("description", description)

    return this.request("/upload", {
      method: "POST",
      headers: {}, // Remove Content-Type to let browser set it for FormData
      body: formData,
    })
  }

  // Dashboard Analytics
  async getDashboardStats() {
    return this.request("/dashboard/stats")
  }

  // Financial Settings
  async getFinancialSettings() {
    return this.request("/financial-settings")
  }

  async updateFinancialSettings(settingsData) {
    return this.request("/financial-settings", {
      method: "PUT",
      body: settingsData,
    })
  }

  // Event Financial Data
  async getEventFinancialData(eventId) {
    return this.request(`/events/${eventId}/financial`)
  }

  async updateEventFinancialData(eventId, financialData) {
    return this.request(`/events/${eventId}/financial`, {
      method: "PUT",
      body: financialData,
    })
  }

  // Analytics (placeholder for future implementation)
  async getAnalytics(timeRange = "6months") {
    // This will be implemented when proper analytics backend is ready
    throw new Error("Analytics endpoint not yet implemented")
  }

  // Notification Settings
  async getNotificationSettings() {
    return this.request("/notification-settings")
  }

  async updateNotificationSettings(settingsData) {
    return this.request("/notification-settings", {
      method: "PUT",
      body: settingsData,
    })
  }

  // Notifications
  async getNotifications() {
    return this.request("/notifications")
  }

  async markNotificationAsRead(notificationId) {
    return this.request(`/notifications/${notificationId}/read`, {
      method: "PUT",
    })
  }

  // Event Vendors
  async getEventVendors(eventId) {
    return this.request(`/events/${eventId}/vendors`)
  }

  async assignVendorToEvent(eventId, vendorData) {
    return this.request(`/events/${eventId}/vendors`, {
      method: "POST",
      body: vendorData,
    })
  }

  async updateEventVendor(eventId, vendorId, vendorData) {
    return this.request(`/events/${eventId}/vendors/${vendorId}`, {
      method: "PUT",
      body: vendorData,
    })
  }

  async removeVendorFromEvent(eventId, vendorId) {
    return this.request(`/events/${eventId}/vendors/${vendorId}`, {
      method: "DELETE",
    })
  }

  // Seating Charts
  async getSeatingChart(eventId) {
    return this.request(`/events/${eventId}/seating`)
  }

  async updateSeatingChart(eventId, seatingData) {
    return this.request(`/events/${eventId}/seating`, {
      method: "PUT",
      body: seatingData,
    })
  }

  // Invitations
  async getInvitations(eventId) {
    return this.request(`/events/${eventId}/invitations`)
  }

  async getInvitationTemplates() {
    return this.request("/invitation-templates")
  }

  async updateEventTemplate(eventId, templateName) {
    return this.request(`/events/${eventId}/template`, {
      method: "PUT",
      body: { template: templateName },
    })
  }

  // Media Management
  async getEventMedia(eventId) {
    return this.request(`/events/${eventId}/media`)
  }

  async deleteMedia(mediaId) {
    return this.request(`/media/${mediaId}`, {
      method: "DELETE",
    })
  }

  // Event Photo Management
  async getEventPhotos(eventId) {
    return this.request(`/events/${eventId}/photos`)
  }

  async uploadEventPhoto(file, eventId, description = "") {
    const formData = new FormData()
    formData.append("file", file)
    formData.append("eventId", eventId)
    if (description) formData.append("description", description)

    return this.request("/upload-event-photo", {
      method: "POST",
      headers: {}, // Remove Content-Type to let browser set it for FormData
      body: formData,
    })
  }

  async deleteEventPhoto(photoId) {
    return this.request(`/photos/${photoId}`, {
      method: "DELETE",
    })
  }

  async updateEventPhoto(photoId, photoData) {
    return this.request(`/photos/${photoId}`, {
      method: "PUT",
      body: photoData,
    })
  }

  async generateEventGalleryLink(eventId) {
    return this.request(`/events/${eventId}/gallery-link`)
  }

  // Vendor Reviews
  async getVendorReviews(vendorId) {
    return this.request(`/vendors/${vendorId}/reviews`)
  }

  async createVendorReview(vendorId, reviewData) {
    return this.request(`/vendors/${vendorId}/reviews`, {
      method: "POST",
      body: reviewData,
    })
  }

  // Event Timeline
  async getEventTimeline(eventId) {
    return this.request(`/events/${eventId}/timeline`)
  }

  async updateEventTimeline(eventId, timelineData) {
    return this.request(`/events/${eventId}/timeline`, {
      method: "PUT",
      body: timelineData,
    })
  }
}

// Create singleton instance
const apiClient = new ApiClient()

export default apiClient

// Named exports for convenience
export const {
  login,
  register,
  logout,
  getEvents,
  createEvent,
  getEvent,
  updateEvent,
  deleteEvent,
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  getVendors,
  createVendor,
  updateVendor,
  deleteVendor,
  getGuests,
  createGuest,
  updateGuest,
  deleteGuest,
  generateQRCode,
  checkInGuest,
  sendInvitations,
  sendThankYouMessages,
  uploadFile,
  getDashboardStats,
  getNotifications,
  markNotificationAsRead,
  getFinancialSettings,
  updateFinancialSettings,
  getEventFinancialData,
  updateEventFinancialData,
  getAnalytics,
  getNotificationSettings,
  updateNotificationSettings,
} = apiClient
