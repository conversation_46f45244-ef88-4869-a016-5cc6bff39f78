"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Mail, Send, Download, Eye, Heart, Calendar, MapPin, Clock } from "lucide-react"

export function InvitationPreview() {
  return (
    <div className="grid lg:grid-cols-2 gap-6">
      {/* Invitation Preview */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-amber-600" />
            Invitation Preview
          </CardTitle>
          <CardDescription>Preview how your invitation will look</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Digital Invitation */}
          <div className="bg-gradient-to-br from-amber-500 to-yellow-500 text-white p-8 rounded-lg text-center">
            <div className="mb-6">
              <Heart className="h-12 w-12 mx-auto mb-4 opacity-80" />
              <h2 className="text-2xl font-bold mb-2">You're Invited!</h2>
            </div>

            <div className="space-y-3 text-lg opacity-90">
              <h3 className="text-xl font-semibold">Ngounou Arlane & Otantik Nuna Wedding</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>December 15, 2024</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>6:00 PM</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>Hilton Hotel Yaoundé</span>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-white/20 rounded-lg">
              <p className="text-sm leading-relaxed">
                Join us as we celebrate this special day with family and friends. Your presence would make our joy
                complete.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invitation Settings */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle>Invitation Settings</CardTitle>
          <CardDescription>Manage invitation distribution and tracking</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">150</div>
              <div className="text-sm text-gray-600">Total Invitations</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">142</div>
              <div className="text-sm text-gray-600">Delivered</div>
            </div>
          </div>

          {/* Actions */}
          <div className="space-y-3">
            <Button className="w-full bg-gradient-to-r from-blue-600 to-cyan-600">
              <Send className="h-4 w-4 mr-2" />
              Send Invitations
            </Button>
            <Button variant="outline" className="w-full bg-transparent">
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
            <Button variant="outline" className="w-full bg-transparent">
              <Eye className="h-4 w-4 mr-2" />
              Preview All Templates
            </Button>
          </div>

          {/* Status */}
          <div className="space-y-3">
            <h4 className="font-medium">Invitation Status</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Delivered</span>
                <Badge variant="default">142</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Opened</span>
                <Badge variant="outline">128</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">RSVP Received</span>
                <Badge variant="outline">142</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Pending</span>
                <Badge variant="secondary">8</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
