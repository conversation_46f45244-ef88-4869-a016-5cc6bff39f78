import apiClient from "@/lib/api"

export interface CSVUploadResult {
  success: boolean
  guests: any[]
  duplicates: any[]
  errors: any[]
  summary: {
    total_processed: number
    successfully_imported: number
    duplicates_found: number
    errors_found: number
  }
}

export interface CSVValidationError {
  row: number
  field: string
  message: string
}

export interface CSVDuplicate {
  name: string
  email?: string
  phone?: string
  table_number: string
  reason: string
  duplicate_type: 'database' | 'file' | 'error'
  rowNumber: number
}

export class CSVUploadService {
  /**
   * Validates CSV file before upload
   */
  static validateFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      return { valid: false, error: 'Only CSV files are allowed' }
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return { valid: false, error: 'File size must be less than 5MB' }
    }

    // Check if file is empty
    if (file.size === 0) {
      return { valid: false, error: 'File cannot be empty' }
    }

    return { valid: true }
  }

  /**
   * Uploads CSV file and imports guests
   */
  static async uploadCSV(eventId: number, file: File): Promise<CSVUploadResult> {
    // Validate file first
    const validation = this.validateFile(file)
    if (!validation.valid) {
      throw new Error(validation.error)
    }

    const formData = new FormData()
    formData.append('file', file)

    try {
      console.log('Uploading CSV to:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests/import-csv`)
      console.log('FormData file:', formData.get('file'))

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests/import-csv`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', response.headers)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Error response:', errorText)
        try {
          const errorData = JSON.parse(errorText)
          throw new Error(errorData.error || 'Failed to upload CSV')
        } catch {
          throw new Error(`HTTP ${response.status}: ${errorText}`)
        }
      }

      const result = await response.json()
      console.log('Upload result:', result)
      return result
    } catch (error) {
      console.error('CSV upload error:', error)
      throw error
    }
  }

  /**
   * Generates a sample CSV template
   */
  static generateSampleCSV(): string {
    const headers = [
      'name',
      'email',
      'phone',
      'table_number',
      'seat_number'
    ]

    const sampleData = [
      ['John Doe', '<EMAIL>', '+1234567890', 'Table 1', 'Seat 1'],
      ['Jane Smith', '<EMAIL>', '+1234567891', 'Table 1', 'Seat 2'],
      ['Bob Johnson', '<EMAIL>', '+1234567892', 'Table 2', 'Seat 1'],
      ['Alice Brown', '<EMAIL>', '+1234567893', 'VIP Table', 'Seat 1']
    ]

    const csvContent = [
      headers.join(','),
      ...sampleData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n')

    return csvContent
  }

  /**
   * Downloads sample CSV template
   */
  static downloadSampleCSV(): void {
    const csvContent = this.generateSampleCSV()
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', 'guest_list_template.csv')
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  /**
   * Formats duplicate information for display
   */
  static formatDuplicateMessage(duplicate: CSVDuplicate): string {
    switch (duplicate.duplicate_type) {
      case 'database':
        return `Guest "${duplicate.name}" already exists in the database`
      case 'file':
        return `Guest "${duplicate.name}" appears multiple times in the uploaded file`
      case 'error':
        return `Error adding guest "${duplicate.name}": ${duplicate.reason}`
      default:
        return `Duplicate: ${duplicate.reason}`
    }
  }

  /**
   * Formats validation error for display
   */
  static formatValidationError(error: CSVValidationError): string {
    return `Row ${error.row}, ${error.field}: ${error.message}`
  }

  /**
   * Groups duplicates by type for better display
   */
  static groupDuplicatesByType(duplicates: CSVDuplicate[]): Record<string, CSVDuplicate[]> {
    return duplicates.reduce((groups, duplicate) => {
      const type = duplicate.duplicate_type
      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(duplicate)
      return groups
    }, {} as Record<string, CSVDuplicate[]>)
  }

  /**
   * Validates CSV content structure (client-side preview)
   */
  static async validateCSVContent(file: File): Promise<{
    valid: boolean
    headers: string[]
    rowCount: number
    errors: string[]
  }> {
    return new Promise((resolve) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const text = e.target?.result as string
          const lines = text.split('\n').filter(line => line.trim())
          
          if (lines.length === 0) {
            resolve({
              valid: false,
              headers: [],
              rowCount: 0,
              errors: ['File is empty']
            })
            return
          }

          const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
          const rowCount = lines.length - 1 // Exclude header
          const errors: string[] = []

          // Check for required headers
          const requiredHeaders = ['name', 'table_number']
          const missingHeaders = requiredHeaders.filter(req => 
            !headers.some(h => h.toLowerCase().includes(req.toLowerCase()))
          )

          if (missingHeaders.length > 0) {
            errors.push(`Missing required columns: ${missingHeaders.join(', ')}`)
          }

          resolve({
            valid: errors.length === 0,
            headers,
            rowCount,
            errors
          })
        } catch (error) {
          resolve({
            valid: false,
            headers: [],
            rowCount: 0,
            errors: ['Failed to parse CSV file']
          })
        }
      }

      reader.onerror = () => {
        resolve({
          valid: false,
          headers: [],
          rowCount: 0,
          errors: ['Failed to read file']
        })
      }

      reader.readAsText(file)
    })
  }
}

export default CSVUploadService
