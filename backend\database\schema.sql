-- Create database
CREATE DATABASE IF NOT EXISTS otantik_ems;
USE otantik_ems;

-- Users table
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'planner', 'vendor', 'guest') NOT NULL,
    region VARCHAR(100),
    phone VARCHAR(20),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Events table
CREATE TABLE events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    venue VARCHAR(255) NOT NULL,
    address TEXT,
    region VARCHAR(100),
    expected_guests INT DEFAULT 0,
    budget DECIMAL(15,2),
    planner_id INT,
    status ENUM('planning', 'confirmed', 'completed', 'cancelled') DEFAULT 'planning',
    invitation_template VARCHAR(50) DEFAULT 'traditional',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (planner_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Guests table
CREATE TABLE guests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    table_number VARCHAR(10),
    seat_number VARCHAR(10),
    dietary_requirements TEXT,
    plus_one BOOLEAN DEFAULT FALSE,
    rsvp_status ENUM('pending', 'confirmed', 'declined') DEFAULT 'pending',
    checked_in BOOLEAN DEFAULT FALSE,
    check_in_time TIMESTAMP NULL,
    whatsapp_sent BOOLEAN DEFAULT FALSE,
    whatsapp_sent_at TIMESTAMP NULL,
    qr_code VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Vendors table
CREATE TABLE vendors (
    id INT PRIMARY KEY AUTO_INCREMENT,
    business_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    service_type ENUM('photographer', 'videographer', 'caterer', 'decorator', 'musician', 'dj', 'florist', 'transportation', 'other') NOT NULL,
    region VARCHAR(100),
    address TEXT,
    description TEXT,
    website VARCHAR(255),
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_events INT DEFAULT 0,
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Event Vendors (Many-to-Many relationship)
CREATE TABLE event_vendors (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    vendor_id INT NOT NULL,
    role VARCHAR(100),
    status ENUM('assigned', 'confirmed', 'completed') DEFAULT 'assigned',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_vendor (event_id, vendor_id)
);

-- Media table
CREATE TABLE media (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    vendor_id INT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type ENUM('image', 'video') NOT NULL,
    file_size INT,
    caption TEXT,
    uploaded_by INT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id) ON DELETE SET NULL,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Notifications table
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    read_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Seating Charts table
CREATE TABLE seating_charts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    table_data JSON,
    layout_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- Financial Settings table
CREATE TABLE financial_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    default_profit_margin DECIMAL(5,2) DEFAULT 20.00,
    currency VARCHAR(3) DEFAULT 'USD',
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    service_fee_percentage DECIMAL(5,2) DEFAULT 0.00,
    payment_terms VARCHAR(100) DEFAULT 'Net 30',
    invoice_prefix VARCHAR(10) DEFAULT 'INV',
    auto_calculate_profit BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_financial_settings (user_id)
);

-- Event Financial Data table
CREATE TABLE event_financial_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    total_budget DECIMAL(15,2) DEFAULT 0.00,
    actual_cost DECIMAL(15,2) DEFAULT 0.00,
    profit_margin DECIMAL(5,2) DEFAULT 0.00,
    calculated_profit DECIMAL(15,2) DEFAULT 0.00,
    service_fee DECIMAL(15,2) DEFAULT 0.00,
    tax_amount DECIMAL(15,2) DEFAULT 0.00,
    final_amount DECIMAL(15,2) DEFAULT 0.00,
    payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
    invoice_number VARCHAR(50),
    invoice_date DATE,
    payment_due_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_financial_data (event_id)
);

-- Expense Categories table
CREATE TABLE expense_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Event Expenses table
CREATE TABLE event_expenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    category_id INT,
    vendor_id INT,
    description VARCHAR(255) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    expense_date DATE NOT NULL,
    receipt_url VARCHAR(500),
    status ENUM('pending', 'approved', 'paid') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES expense_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (vendor_id) REFERENCES vendors(id) ON DELETE SET NULL
);

DROP TABLE IF EXISTS `event_photos`;
CREATE TABLE IF NOT EXISTS `event_photos` (
  `id` int NOT NULL AUTO_INCREMENT,
  `event_id` int NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int DEFAULT NULL,
  `caption` text,
  `uploaded_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `event_id` (`event_id`),
  KEY `uploaded_by` (`uploaded_by`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Déchargement des données de la table `event_photos`
--

INSERT INTO `event_photos` (`id`, `event_id`, `filename`, `original_name`, `file_path`, `file_size`, `caption`, `uploaded_by`, `created_at`, `updated_at`) VALUES
(1, 2, 'file-1751937362525-77910058.PNG', '5.PNG', '/uploads/file-1751937362525-77910058.PNG', 35246, 'Event photo - 5.PNG', 1, '2025-07-08 01:16:02', '2025-07-08 01:16:02'),
(2, 2, 'file-1751937392732-369823411.PNG', '7.PNG', '/uploads/file-1751937392732-369823411.PNG', 62192, 'Event photo - 7.PNG', 1, '2025-07-08 01:16:32', '2025-07-08 01:16:32'),
(3, 2, 'file-1751937392661-284669927.PNG', '12.PNG', '/uploads/file-1751937392661-284669927.PNG', 73938, 'Event photo - 12.PNG', 1, '2025-07-08 01:16:32', '2025-07-08 01:16:32'),
(4, 2, 'file-1751937392637-217885194.PNG', '6.PNG', '/uploads/file-1751937392637-217885194.PNG', 62999, 'Event photo - 6.PNG', 1, '2025-07-08 01:16:32', '2025-07-08 01:16:32'),
(5, 2, 'file-1751937392726-534941073.PNG', '10.PNG', '/uploads/file-1751937392726-534941073.PNG', 80228, 'Event photo - 10.PNG', 1, '2025-07-08 01:16:32', '2025-07-08 01:16:32'),
(6, 1, 'file-1751979500233-784824861.PNG', 'ABOUT D.PNG', '/uploads/file-1751979500233-784824861.PNG', 277578, 'Event photo - ABOUT D.PNG', 1, '2025-07-08 12:58:20', '2025-07-08 12:58:20');

DROP TABLE IF EXISTS `present_guests`;
CREATE TABLE IF NOT EXISTS `present_guests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `event_id` int NOT NULL,
  `guest_id` int NOT NULL,
  `check_in_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `check_in_method` enum('qr_scan','manual_search') NOT NULL,
  `checked_by` int DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_event_guest` (`event_id`,`guest_id`),
  KEY `guest_id` (`guest_id`),
  KEY `checked_by` (`checked_by`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- Create indexes for better performance
CREATE INDEX idx_events_planner ON events(planner_id);
CREATE INDEX idx_events_date ON events(event_date);
CREATE INDEX idx_guests_event ON guests(event_id);
CREATE INDEX idx_guests_rsvp ON guests(rsvp_status);
CREATE INDEX idx_vendors_type ON vendors(service_type);
CREATE INDEX idx_vendors_region ON vendors(region);
CREATE INDEX idx_media_event ON media(event_id);
CREATE INDEX idx_notifications_user ON notifications(user_id);
