"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Search, Eye, Download, Share2, ImageIcon, Users, Calendar, Loader2 } from "lucide-react"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export function EventGallery() {
  const [eventGalleries, setEventGalleries] = useState([])
  const [searchTerm, setSearchTerm] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [stats, setStats] = useState({
    totalGalleries: 0,
    totalPhotos: 0,
    totalDownloads: 0,
  })

  useEffect(() => {
    loadEventGalleries()
  }, [])

  const loadEventGalleries = async () => {
    try {
      setIsLoading(true)
      const events = await apiClient.getEvents()

      // Transform events data to gallery format
      const galleries = await Promise.all(
        events.map(async (event) => {
          try {
            const media = await apiClient.getEventMedia(event.id)
            return {
              id: event.id,
              eventName: event.title,
              date: new Date(event.event_date).toLocaleDateString(),
              totalPhotos: media.length,
              totalViews: Math.floor(Math.random() * 1000) + 100, // Simulated for now
              downloads: Math.floor(Math.random() * 100) + 10, // Simulated for now
              coverImage: media.length > 0 ? media[0].file_url : "/placeholder.svg?height=200&width=300",
              status: event.status === "active" ? "published" : "draft",
            }
          } catch (error) {
            return {
              id: event.id,
              eventName: event.title,
              date: new Date(event.event_date).toLocaleDateString(),
              totalPhotos: 0,
              totalViews: 0,
              downloads: 0,
              coverImage: "/placeholder.svg?height=200&width=300",
              status: "draft",
            }
          }
        }),
      )

      setEventGalleries(galleries)

      // Calculate stats
      const totalGalleries = galleries.length
      const totalPhotos = galleries.reduce((sum, gallery) => sum + gallery.totalPhotos, 0)
      const totalDownloads = galleries.reduce((sum, gallery) => sum + gallery.downloads, 0)

      setStats({ totalGalleries, totalPhotos, totalDownloads })
    } catch (error) {
      console.error("Failed to load event galleries:", error)
      toast.error("Failed to load event galleries")
    } finally {
      setIsLoading(false)
    }
  }

  const handleViewGallery = async (galleryId) => {
    try {
      const media = await apiClient.getEventMedia(galleryId)
      // Here you would typically open a modal or navigate to a detailed view
      console.log("Gallery media:", media)
      toast.success("Gallery loaded successfully")
    } catch (error) {
      toast.error("Failed to load gallery")
    }
  }

  const handleShareGallery = async (galleryId) => {
    try {
      // Generate shareable link
      const shareUrl = `${window.location.origin}/gallery/${galleryId}`
      await navigator.clipboard.writeText(shareUrl)
      toast.success("Gallery link copied to clipboard")
    } catch (error) {
      toast.error("Failed to copy gallery link")
    }
  }

  const filteredGalleries = eventGalleries.filter((gallery) =>
    gallery.eventName.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const getStatusBadge = (status) => {
    switch (status) {
      case "published":
        return <Badge className="bg-green-100 text-green-800">Published</Badge>
      case "draft":
        return <Badge variant="secondary">Draft</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading galleries...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Event Galleries</CardTitle>
              <CardDescription>Manage and share your event photo galleries</CardDescription>
            </div>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search galleries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-80"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGalleries.map((gallery) => (
              <Card key={gallery.id} className="group hover:shadow-lg transition-shadow">
                <div className="relative overflow-hidden">
                  <img
                    src={gallery.coverImage || "/placeholder.svg"}
                    alt={gallery.eventName}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform"
                  />
                  <div className="absolute top-4 right-4">{getStatusBadge(gallery.status)}</div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{gallery.eventName}</h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {gallery.date}
                    </div>
                    <div className="flex items-center gap-1">
                      <ImageIcon className="h-4 w-4" />
                      {gallery.totalPhotos}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="font-semibold text-blue-600">{gallery.totalViews}</div>
                      <div className="text-blue-600">Views</div>
                    </div>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="font-semibold text-green-600">{gallery.downloads}</div>
                      <div className="text-green-600">Downloads</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 bg-transparent"
                      onClick={() => handleViewGallery(gallery.id)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 bg-transparent"
                      onClick={() => handleShareGallery(gallery.id)}
                    >
                      <Share2 className="h-4 w-4 mr-1" />
                      Share
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredGalleries.length === 0 && (
            <div className="text-center py-12">
              <ImageIcon className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No galleries found</h3>
              <p className="text-gray-600">Upload media to your assigned events to create galleries</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Gallery Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Galleries</p>
                <p className="text-3xl font-bold text-blue-600 mt-2">{stats.totalGalleries}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <ImageIcon className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Photos</p>
                <p className="text-3xl font-bold text-green-600 mt-2">{stats.totalPhotos}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Downloads</p>
                <p className="text-3xl font-bold text-amber-600 mt-2">{stats.totalDownloads}</p>
              </div>
              <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                <Download className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
