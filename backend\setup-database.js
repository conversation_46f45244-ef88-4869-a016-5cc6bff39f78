const mysql = require("mysql2/promise")
const fs = require("fs")
const path = require("path")

// Database configuration - adjust these values according to your phpMyAdmin setup
const dbConfig = {
  host: "localhost",
  user: "root", // Change this to your MySQL username
  password: "", // Change this to your MySQL password
  database: "otantik_ems", // Your database name
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
}

async function setupDatabase() {
  let connection

  try {
    console.log("🔌 Connecting to MySQL database...")
    
    // First, connect without specifying database to create it if it doesn't exist
    const connectionWithoutDB = await mysql.createConnection({
      host: dbConfig.host,
      user: dbConfig.user,
      password: dbConfig.password,
    })

    // Create database if it doesn't exist
    await connectionWithoutDB.execute(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database}`)
    console.log(`✅ Database '${dbConfig.database}' is ready`)

    await connectionWithoutDB.end()

    // Now connect to the specific database
    connection = await mysql.createConnection(dbConfig)
    console.log("✅ Connected to MySQL database successfully!")

    // Read and execute the schema file
    const schemaPath = path.join(__dirname, "database", "schema-clean.sql")
    const schema = fs.readFileSync(schemaPath, "utf8")

    // Split the schema into individual statements
    const statements = schema
      .split(";")
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith("--"))

    console.log("📋 Creating database tables...")

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement)
          console.log(`✅ Executed: ${statement.substring(0, 50)}...`)
        } catch (error) {
          if (error.code === "ER_TABLE_EXISTS_ERROR") {
            console.log(`⚠️  Table already exists, skipping...`)
          } else {
            console.error(`❌ Error executing statement: ${error.message}`)
          }
        }
      }
    }

    // Create a test user for login
    const bcrypt = require("bcryptjs")
    const hashedPassword = await bcrypt.hash("password123", 10)

    try {
      await connection.execute(`
        INSERT INTO users (first_name, last_name, email, password, role, region, phone) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, ["Test", "Planner", "<EMAIL>", hashedPassword, "planner", "centre", "1234567890"])
      console.log("✅ Created test planner user: <EMAIL> / password123")
    } catch (error) {
      if (error.code === "ER_DUP_ENTRY") {
        console.log("⚠️  Test user already exists")
      } else {
        console.error("❌ Error creating test user:", error.message)
      }
    }

    console.log("\n🎉 Database setup completed successfully!")
    console.log("📝 You can now login with:")
    console.log("   Email: <EMAIL>")
    console.log("   Password: password123")

  } catch (error) {
    console.error("❌ Database setup failed:", error.message)
    
    if (error.code === "ECONNREFUSED") {
      console.log("\n💡 Troubleshooting tips:")
      console.log("1. Make sure MySQL is running")
      console.log("2. Check your MySQL credentials in the dbConfig object")
      console.log("3. Verify the database name is correct")
      console.log("4. Make sure you have permission to create databases")
    }
    
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

setupDatabase() 