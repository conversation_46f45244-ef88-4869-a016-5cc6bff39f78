"use client"

import { useState, useEffect } from "react"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import { X, CheckCircle, AlertTriangle, Info, Loader2 } from "lucide-react"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export function NotificationBar() {
  const [notifications, setNotifications] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadNotifications()
    // Set up polling for new notifications
    const interval = setInterval(loadNotifications, 60000) // Check every minute
    return () => clearInterval(interval)
  }, [])

  const loadNotifications = async () => {
    try {
      const notificationsData = await apiClient.getNotifications()

      // Transform backend notifications to component format
      const formattedNotifications = notificationsData.map((notification) => ({
        id: notification.id,
        type: getNotificationType(notification.type),
        title: notification.title,
        message: notification.message,
        time: getTimeAgo(notification.created_at),
        read: notification.read_status,
      }))

      // Only show unread notifications
      const unreadNotifications = formattedNotifications.filter((n) => !n.read)
      setNotifications(unreadNotifications)
    } catch (error) {
      console.error("Failed to load notifications:", error)
      // Don't show error toast for notifications as it's not critical
    } finally {
      setIsLoading(false)
    }
  }

  const getNotificationType = (backendType) => {
    switch (backendType) {
      case "event_created":
      case "guest_added":
      case "media_uploaded":
        return "success"
      case "vendor_unresponsive":
      case "payment_due":
      case "deadline_approaching":
        return "warning"
      case "system_update":
      case "new_feature":
        return "info"
      case "error":
      case "event_cancelled":
        return "error"
      default:
        return "info"
    }
  }

  const getTimeAgo = (timestamp) => {
    const now = new Date()
    const notificationTime = new Date(timestamp)
    const diffInMinutes = Math.floor((now - notificationTime) / (1000 * 60))

    if (diffInMinutes < 1) return "Just now"
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours} hours ago`

    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} days ago`
  }

  const dismissNotification = async (id) => {
    try {
      await apiClient.markNotificationAsRead(id)
      setNotifications((prev) => prev.filter((n) => n.id !== id))
    } catch (error) {
      console.error("Failed to dismiss notification:", error)
      toast.error("Failed to dismiss notification")
    }
  }

  const dismissAllNotifications = async () => {
    try {
      await apiClient.markAllNotificationsAsRead()
      setNotifications([])
      toast.success("All notifications dismissed")
    } catch (error) {
      console.error("Failed to dismiss all notifications:", error)
      toast.error("Failed to dismiss notifications")
    }
  }

  if (isLoading) {
    return (
      <div className="bg-blue-50 border-b border-blue-200 p-4">
        <div className="flex items-center justify-center">
          <Loader2 className="h-4 w-4 animate-spin mr-2" />
          <span className="text-sm text-gray-600">Loading notifications...</span>
        </div>
      </div>
    )
  }

  if (notifications.length === 0) return null

  return (
    <div className="bg-blue-50 border-b border-blue-200 p-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-blue-900">Notifications ({notifications.length})</h3>
        {notifications.length > 1 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={dismissAllNotifications}
            className="text-blue-600 hover:text-blue-800"
          >
            Dismiss All
          </Button>
        )}
      </div>

      <div className="space-y-2">
        {notifications.map((notification) => (
          <Alert key={notification.id} className="border-0 bg-white/80">
            <div className="flex items-start gap-3">
              {notification.type === "success" && <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />}
              {notification.type === "warning" && <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5" />}
              {notification.type === "info" && <Info className="h-4 w-4 text-blue-600 mt-0.5" />}
              {notification.type === "error" && <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />}

              <div className="flex-1">
                <AlertDescription>
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-medium">{notification.title}</span>
                      <span className="text-gray-600 ml-2">{notification.message}</span>
                      <span className="text-xs text-gray-500 ml-2">• {notification.time}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => dismissNotification(notification.id)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </AlertDescription>
              </div>
            </div>
          </Alert>
        ))}
      </div>
    </div>
  )
}
