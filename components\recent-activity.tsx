"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, Users, UserCheck, Mail } from "lucide-react"

const activities = [
  {
    id: 1,
    type: "event_created",
    title: "New event created",
    description: "Holiday Gala 2024",
    time: "2 hours ago",
    icon: Calendar,
    color: "text-blue-600",
    bgColor: "bg-blue-100",
  },
  {
    id: 2,
    type: "guest_checkin",
    title: "Guest checked in",
    description: "Alice Johnson - Table 1",
    time: "5 minutes ago",
    icon: UserCheck,
    color: "text-green-600",
    bgColor: "bg-green-100",
  },
  {
    id: 3,
    type: "invitation_sent",
    title: "Invitations sent",
    description: "150 invitations delivered",
    time: "1 hour ago",
    icon: Mail,
    color: "text-purple-600",
    bgColor: "bg-purple-100",
  },
  {
    id: 4,
    type: "guest_rsvp",
    title: "RSVP received",
    description: "<PERSON> confirmed attendance",
    time: "3 hours ago",
    icon: Users,
    color: "text-orange-600",
    bgColor: "bg-orange-100",
  },
]

export function RecentActivity() {
  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>Latest updates across all events</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3">
              <div className={`h-8 w-8 ${activity.bgColor} rounded-lg flex items-center justify-center flex-shrink-0`}>
                <activity.icon className={`h-4 w-4 ${activity.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                <p className="text-sm text-gray-600">{activity.description}</p>
                <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
