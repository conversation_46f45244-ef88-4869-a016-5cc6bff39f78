"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Camera, Mail, MapPin, Phone, User } from "lucide-react"

interface CreateVendorDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit?: (vendorData: any) => void
}

export function CreateVendorDialog({ open, onOpenChange, onSubmit }: CreateVendorDialogProps) {
  const [vendorData, setVendorData] = useState({
    businessName: "",
    contactPerson: "",
    email: "",
    phone: "",
    type: "",
    region: "",
    address: "",
    description: "",
    website: "",
    password: "",
  })
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (onSubmit) {
        await onSubmit(vendorData)
      }

      // Reset form
      setVendorData({
        businessName: "",
        contactPerson: "",
        email: "",
        phone: "",
        type: "",
        region: "",
        address: "",
        description: "",
        website: "",
        password: "",
      })
    } catch (error) {
      console.error("Error creating vendor:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Vendor</DialogTitle>
          <DialogDescription>Register a new vendor in the system.</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="businessName">Business Name *</Label>
              <div className="relative">
                <Camera className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="businessName"
                  placeholder="e.g., Ange Photography"
                  value={vendorData.businessName}
                  onChange={(e) => setVendorData({ ...vendorData, businessName: e.target.value })}
                  className="pl-10"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="contactPerson">Contact Person *</Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="contactPerson"
                  placeholder="e.g., Ange Marie"
                  value={vendorData.contactPerson}
                  onChange={(e) => setVendorData({ ...vendorData, contactPerson: e.target.value })}
                  className="pl-10"
                  required
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={vendorData.email}
                  onChange={(e) => setVendorData({ ...vendorData, email: e.target.value })}
                  className="pl-10"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number *</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  placeholder="+237 6XX XXX XXX"
                  value={vendorData.phone}
                  onChange={(e) => setVendorData({ ...vendorData, phone: e.target.value })}
                  className="pl-10"
                  required
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Service Type *</Label>
              <Select value={vendorData.type} onValueChange={(value) => setVendorData({ ...vendorData, type: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select service type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="photographer">Photographer</SelectItem>
                  <SelectItem value="videographer">Videographer</SelectItem>
                  <SelectItem value="caterer">Caterer</SelectItem>
                  <SelectItem value="decorator">Decorator</SelectItem>
                  <SelectItem value="musician">Musician</SelectItem>
                  <SelectItem value="dj">DJ</SelectItem>
                  <SelectItem value="florist">Florist</SelectItem>
                  <SelectItem value="transportation">Transportation</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="region">Region *</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />
                <Select
                  value={vendorData.region}
                  onValueChange={(value) => setVendorData({ ...vendorData, region: value })}
                >
                  <SelectTrigger className="pl-10">
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="centre">Centre Region</SelectItem>
                    <SelectItem value="littoral">Littoral Region</SelectItem>
                    <SelectItem value="northwest">Northwest Region</SelectItem>
                    <SelectItem value="southwest">Southwest Region</SelectItem>
                    <SelectItem value="west">West Region</SelectItem>
                    <SelectItem value="east">East Region</SelectItem>
                    <SelectItem value="north">North Region</SelectItem>
                    <SelectItem value="adamawa">Adamawa Region</SelectItem>
                    <SelectItem value="far-north">Far North Region</SelectItem>
                    <SelectItem value="south">South Region</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Business Address</Label>
            <Input
              id="address"
              placeholder="Full business address"
              value={vendorData.address}
              onChange={(e) => setVendorData({ ...vendorData, address: e.target.value })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="website">Website (Optional)</Label>
            <Input
              id="website"
              placeholder="https://www.example.com"
              value={vendorData.website}
              onChange={(e) => setVendorData({ ...vendorData, website: e.target.value })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Business Description</Label>
            <Textarea
              id="description"
              placeholder="Brief description of services offered..."
              value={vendorData.description}
              onChange={(e) => setVendorData({ ...vendorData, description: e.target.value })}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Temporary Password *</Label>
            <Input
              id="password"
              type="password"
              placeholder="Enter temporary password"
              value={vendorData.password}
              onChange={(e) => setVendorData({ ...vendorData, password: e.target.value })}
              required
            />
            <p className="text-xs text-gray-500">Vendor will be prompted to change password on first login</p>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" className="bg-gradient-to-r from-amber-500 to-yellow-500" disabled={loading}>
              {loading ? "Creating..." : "Create Vendor"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
