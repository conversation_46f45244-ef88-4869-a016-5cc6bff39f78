const mysql = require("mysql2/promise")
const fs = require("fs")
const path = require("path")
require("dotenv").config()

async function runMigrations() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    multipleStatements: true,
  })

  try {
    console.log("Running database migrations...")

    // First create the database
    await connection.execute("CREATE DATABASE IF NOT EXISTS otantik_ems")
    console.log("✅ Database created!")

    // Close connection and reconnect to the specific database
    await connection.end()

    const dbConnection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "root",
      password: process.env.DB_PASSWORD || "",
      database: "otantik_ems",
      multipleStatements: true,
    })

    // Read schema file and split into individual statements
    let schemaSQL = fs.readFileSync(path.join(__dirname, "../database/schema.sql"), "utf8")

    // Remove the CREATE DATABASE and USE statements
    schemaSQL = schemaSQL.replace(/-- Create database[\s\S]*?USE otantik_ems;\s*/i, '')

    // Split into individual statements and execute them one by one
    const statements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await dbConnection.execute(statement)
        } catch (error) {
          console.log(`Error executing: ${statement.substring(0, 50)}...`)
          throw error
        }
      }
    }

    await dbConnection.end()

    console.log("✅ Database schema created successfully!")
  } catch (error) {
    console.error("❌ Migration failed:", error)
    process.exit(1)
  }
}

runMigrations()
