"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Users, Plus, Search, Filter, Mail, Phone, MoreHorizontal, Upload, Download, AlertCircle, CheckCircle, XCircle, Loader2 } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON>Des<PERSON>, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { PlannerSidebar } from "@/components/planner-sidebar"
import { guestService } from "@/lib/services/guest.service"
import CSVUploadService, { CSVUploadResult, CSVDuplicate, CSVValidationError } from "@/lib/services/csv-upload.service"
import DuplicateNotification from "@/components/duplicate-notification"
import { toast } from "@/components/ui/use-toast"

interface Guest {
  id: number
  name: string
  email: string
  phone: string
  rsvp_status: string
  table_number: string
  seat_number: string
  checked_in: boolean
}

export default function PlannerGuests() {
  const [guests, setGuests] = useState<Guest[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [rsvpFilter, setRsvpFilter] = useState("all")
  const [uploadLoading, setUploadLoading] = useState(false)
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [uploadResult, setUploadResult] = useState<CSVUploadResult | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [fileValidation, setFileValidation] = useState<{
    valid: boolean
    headers: string[]
    rowCount: number
    errors: string[]
  } | null>(null)
  const [editingGuest, setEditingGuest] = useState<Guest | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [newGuest, setNewGuest] = useState({
    name: '',
    email: '',
    phone: '',
    table_number: '',
    seat_number: ''
  })
  const fileInputRef = useRef<HTMLInputElement>(null)

  // TODO: Replace with actual eventId from props/router
  const eventId = 1

  useEffect(() => {
    fetchGuests()
  }, [])

  const fetchGuests = async () => {
    try {
      setLoading(true)
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests`, {
        credentials: 'include'
      })
      if (response.ok) {
        const data = await response.json()
        setGuests(data)
      }
    } catch (error) {
      console.error("Failed to fetch guests:", error)
      toast({
        title: "Error",
        description: "Failed to load guests",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const filteredGuests = guests.filter((guest) => {
    const matchesSearch =
      guest.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (guest.email && guest.email.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesRsvp = rsvpFilter === "all" || guest.rsvp_status === rsvpFilter
    return matchesSearch && matchesRsvp
  })

  const getRsvpBadge = (rsvp: string) => {
    switch (rsvp) {
      case "confirmed":
        return <Badge className="bg-green-100 text-green-800">Confirmed</Badge>
      case "pending":
        return <Badge className="bg-amber-100 text-amber-800">Pending</Badge>
      case "declined":
        return <Badge className="bg-red-100 text-red-800">Declined</Badge>
      default:
        return <Badge variant="outline">{rsvp}</Badge>
    }
  }

  const handleCreateGuest = async (guestData: any) => {
    try {
      const newGuest = await guestService.createGuest(guestData)
      setGuests([...guests, newGuest])
    } catch (error) {
      console.error("Failed to create guest:", error)
    }
  }

  const handleUpdateRsvp = async (guestId: number, rsvp: string) => {
    try {
      await guestService.updateRsvp(guestId, rsvp)
      setGuests(guests.map((guest) => (guest.id === guestId ? { ...guest, rsvp_status: rsvp } : guest)))
    } catch (error) {
      console.error("Failed to update RSVP:", error)
    }
  }

  const handleImportClick = () => {
    setShowUploadDialog(true)
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setSelectedFile(file)

    // Validate file
    const validation = CSVUploadService.validateFile(file)
    if (!validation.valid) {
      toast({
        title: "Invalid File",
        description: validation.error,
        variant: "destructive"
      })
      return
    }

    // Preview CSV content
    try {
      const preview = await CSVUploadService.validateCSVContent(file)
      setFileValidation(preview)
    } catch (error) {
      console.error("Failed to preview CSV:", error)
    }
  }

  const handleUploadCSV = async () => {
    if (!selectedFile) return

    try {
      setUploadLoading(true)
      const result = await CSVUploadService.uploadCSV(eventId, selectedFile)
      setUploadResult(result)

      if (result.success) {
        setGuests(result.guests)
        toast({
          title: "CSV Imported Successfully",
          description: `${result.summary.successfully_imported} guests imported, ${result.summary.duplicates_found} duplicates found`,
        })
      }
    } catch (error: any) {
      toast({
        title: "Upload Failed",
        description: error.message,
        variant: "destructive"
      })
    } finally {
      setUploadLoading(false)
    }
  }

  const handleCloseUploadDialog = () => {
    setShowUploadDialog(false)
    setSelectedFile(null)
    setFileValidation(null)
    setUploadResult(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const downloadSampleCSV = () => {
    CSVUploadService.downloadSampleCSV()
  }

  const handleAddGuest = async () => {
    if (!newGuest.name.trim() || !newGuest.table_number.trim()) {
      toast({
        title: "Validation Error",
        description: "Name and Table Number are required",
        variant: "destructive"
      })
      return
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(newGuest)
      })

      if (response.ok) {
        const addedGuest = await response.json()
        setGuests([...guests, addedGuest])
        setShowAddDialog(false)
        setNewGuest({
          name: '',
          email: '',
          phone: '',
          table_number: '',
          seat_number: ''
        })
        toast({
          title: "Guest Added",
          description: "Guest has been added successfully"
        })
        fetchGuests() // Refresh the list to ensure proper sorting
      } else {
        throw new Error('Failed to add guest')
      }
    } catch (error) {
      toast({
        title: "Add Failed",
        description: "Failed to add guest",
        variant: "destructive"
      })
    }
  }

  const handleEditGuest = (guest: Guest) => {
    setEditingGuest({ ...guest })
    setShowEditDialog(true)
  }

  const handleUpdateGuest = async () => {
    if (!editingGuest) return

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests/${editingGuest.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          name: editingGuest.name,
          email: editingGuest.email,
          phone: editingGuest.phone,
          table_number: editingGuest.table_number,
          seat_number: editingGuest.seat_number
        })
      })

      if (response.ok) {
        const updatedGuest = await response.json()
        setGuests(guests.map(g => g.id === editingGuest.id ? updatedGuest : g))
        setShowEditDialog(false)
        setEditingGuest(null)
        toast({
          title: "Guest Updated",
          description: "Guest information has been updated successfully"
        })
      } else {
        throw new Error('Failed to update guest')
      }
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update guest information",
        variant: "destructive"
      })
    }
  }

  const handleDeleteGuest = async (guestId: number) => {
    if (!confirm('Are you sure you want to delete this guest?')) return

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests/${guestId}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (response.ok) {
        setGuests(guests.filter(g => g.id !== guestId))
        toast({
          title: "Guest Deleted",
          description: "Guest has been removed from the list"
        })
      } else {
        throw new Error('Failed to delete guest')
      }
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Failed to delete guest",
        variant: "destructive"
      })
    }
  }

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <PlannerSidebar />
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Guest Management</h1>
              <p className="text-gray-600">Manage guest list and RSVPs</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleImportClick}>
                <Upload className="h-4 w-4 mr-2" />
                Import CSV
              </Button>
              <Button
                className="bg-gradient-to-r from-blue-600 to-cyan-600"
                onClick={() => setShowAddDialog(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Guest
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Guests</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{guests.length}</p>
                  </div>
                  <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Confirmed</p>
                    <p className="text-3xl font-bold text-green-600 mt-2">
                      {guests.filter((g) => g.rsvp_status === "confirmed").length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-3xl font-bold text-amber-600 mt-2">
                      {guests.filter((g) => g.rsvp_status === "pending").length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-amber-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Checked In</p>
                    <p className="text-3xl font-bold text-purple-600 mt-2">
                      {guests.filter((g) => g.checkedIn).length}
                    </p>
                  </div>
                  <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Show duplicates notification if any */}
          {uploadResult && uploadResult.duplicates.length > 0 && (
            <div className="mb-6">
              <DuplicateNotification duplicates={uploadResult.duplicates} />
            </div>
          )}

          {/* Guests Table */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    Guest List
                  </CardTitle>
                  <CardDescription>Manage all event guests and their details</CardDescription>
                </div>
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search guests..."
                      className="pl-10 w-80"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <Filter className="h-4 w-4 mr-2" />
                        RSVP: {rsvpFilter === "all" ? "All" : rsvpFilter}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => setRsvpFilter("all")}>All Status</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setRsvpFilter("confirmed")}>Confirmed</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setRsvpFilter("pending")}>Pending</DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setRsvpFilter("declined")}>Declined</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Guest</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>RSVP Status</TableHead>
                      <TableHead>Table</TableHead>
                      <TableHead>Check-in</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredGuests.map((guest) => (
                      <TableRow key={guest.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar>
                              <AvatarFallback>
                                {guest.name
                                  .split(" ")
                                  .map((n) => n[0])
                                  .join("")}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{guest.name}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-1 text-sm">
                              <Mail className="h-3 w-3 text-gray-500" />
                              {guest.email}
                            </div>
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3 text-gray-500" />
                              {guest.phone}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getRsvpBadge(guest.rsvp_status)}</TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <Badge variant="outline">{guest.table_number}</Badge>
                            {guest.seat_number && (
                              <div className="text-xs text-gray-500">Seat: {guest.seat_number}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {guest.checked_in ? (
                            <Badge className="bg-green-100 text-green-800">Checked In</Badge>
                          ) : (
                            <Badge variant="secondary">Not Arrived</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditGuest(guest)}>
                                Edit Guest
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleUpdateRsvp(guest.id, guest.rsvp_status === 'confirmed' ? 'pending' : 'confirmed')}>
                                {guest.rsvp_status === 'confirmed' ? 'Mark Pending' : 'Mark Confirmed'}
                              </DropdownMenuItem>
                              <DropdownMenuItem>Send Invitation</DropdownMenuItem>
                              <DropdownMenuItem>Generate QR Code</DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-red-600"
                                onClick={() => handleDeleteGuest(guest.id)}
                              >
                                Remove Guest
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add Guest Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Guest</DialogTitle>
            <DialogDescription>
              Add a new guest to the event
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Name *</label>
                <Input
                  value={newGuest.name}
                  onChange={(e) => setNewGuest({ ...newGuest, name: e.target.value })}
                  placeholder="Guest name"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <Input
                  type="email"
                  value={newGuest.email}
                  onChange={(e) => setNewGuest({ ...newGuest, email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Phone</label>
                <Input
                  value={newGuest.phone}
                  onChange={(e) => setNewGuest({ ...newGuest, phone: e.target.value })}
                  placeholder="+1234567890"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Table Number *</label>
                <Input
                  value={newGuest.table_number}
                  onChange={(e) => setNewGuest({ ...newGuest, table_number: e.target.value })}
                  placeholder="Table 1"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Seat Number</label>
              <Input
                value={newGuest.seat_number}
                onChange={(e) => setNewGuest({ ...newGuest, seat_number: e.target.value })}
                placeholder="Seat 1"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddGuest}>
              Add Guest
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Guest Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Guest</DialogTitle>
            <DialogDescription>
              Update guest information and seating details
            </DialogDescription>
          </DialogHeader>

          {editingGuest && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Name *</label>
                  <Input
                    value={editingGuest.name}
                    onChange={(e) => setEditingGuest({ ...editingGuest, name: e.target.value })}
                    placeholder="Guest name"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email</label>
                  <Input
                    type="email"
                    value={editingGuest.email || ''}
                    onChange={(e) => setEditingGuest({ ...editingGuest, email: e.target.value })}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Phone</label>
                  <Input
                    value={editingGuest.phone || ''}
                    onChange={(e) => setEditingGuest({ ...editingGuest, phone: e.target.value })}
                    placeholder="+1234567890"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Table Number *</label>
                  <Input
                    value={editingGuest.table_number}
                    onChange={(e) => setEditingGuest({ ...editingGuest, table_number: e.target.value })}
                    placeholder="Table 1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Seat Number</label>
                <Input
                  value={editingGuest.seat_number || ''}
                  onChange={(e) => setEditingGuest({ ...editingGuest, seat_number: e.target.value })}
                  placeholder="Seat 1"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateGuest}>
              Update Guest
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* CSV Upload Dialog */}
      <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Import Guests from CSV</DialogTitle>
            <DialogDescription>
              Upload a CSV file to import multiple guests at once. Guests will be sorted by table number.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {!uploadResult && (
              <>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <input
                    type="file"
                    accept=".csv"
                    ref={fileInputRef}
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-sm text-gray-600 mb-2">
                    Click to select a CSV file or drag and drop
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadLoading}
                  >
                    Select CSV File
                  </Button>
                </div>

                {selectedFile && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{selectedFile.name}</p>
                        <p className="text-sm text-gray-500">
                          {(selectedFile.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedFile(null)
                          setFileValidation(null)
                          if (fileInputRef.current) fileInputRef.current.value = ''
                        }}
                      >
                        <XCircle className="h-4 w-4" />
                      </Button>
                    </div>

                    {fileValidation && (
                      <div className="space-y-2">
                        {fileValidation.valid ? (
                          <Alert>
                            <CheckCircle className="h-4 w-4" />
                            <AlertTitle>File Valid</AlertTitle>
                            <AlertDescription>
                              Found {fileValidation.rowCount} rows with columns: {fileValidation.headers.join(', ')}
                            </AlertDescription>
                          </Alert>
                        ) : (
                          <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>File Issues</AlertTitle>
                            <AlertDescription>
                              <ul className="list-disc list-inside">
                                {fileValidation.errors.map((error, index) => (
                                  <li key={index}>{error}</li>
                                ))}
                              </ul>
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </>
            )}

            {uploadResult && (
              <div className="space-y-4">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>Upload Complete</AlertTitle>
                  <AlertDescription>
                    Successfully imported {uploadResult.summary.successfully_imported} guests.
                    {uploadResult.summary.duplicates_found > 0 && (
                      <span> {uploadResult.summary.duplicates_found} duplicates were skipped.</span>
                    )}
                  </AlertDescription>
                </Alert>

                {uploadResult.duplicates.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Duplicates Found:</h4>
                    <div className="max-h-40 overflow-y-auto space-y-1">
                      {uploadResult.duplicates.map((duplicate, index) => (
                        <div key={index} className="text-sm p-2 bg-amber-50 rounded border">
                          <strong>{duplicate.name}</strong> - {CSVUploadService.formatDuplicateMessage(duplicate)}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {uploadResult.errors.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Errors:</h4>
                    <div className="max-h-40 overflow-y-auto space-y-1">
                      {uploadResult.errors.map((error, index) => (
                        <div key={index} className="text-sm p-2 bg-red-50 rounded border">
                          {CSVUploadService.formatValidationError(error)}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          <DialogFooter>
            {!uploadResult ? (
              <>
                <Button variant="outline" onClick={handleCloseUploadDialog}>
                  Cancel
                </Button>
                <Button
                  onClick={handleUploadCSV}
                  disabled={!selectedFile || !fileValidation?.valid || uploadLoading}
                >
                  {uploadLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    'Import Guests'
                  )}
                </Button>
              </>
            ) : (
              <Button onClick={handleCloseUploadDialog}>
                Close
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
