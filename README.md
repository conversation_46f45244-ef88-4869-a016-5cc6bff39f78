# OtantikEMS - Event Management System

A comprehensive event management system designed for Cameroon, supporting traditional weddings, corporate events, and cultural celebrations across all 10 regions.

## Features

- **Multi-Role Dashboard**: Admin, Event Planner, Vendor, and Guest interfaces
- **Guest Management**: RSVP tracking, seating arrangements, dietary requirements
- **WhatsApp Integration**: Send invitations and thank you messages
- **Vendor Coordination**: Photographer, caterer, decorator, and musician management
- **QR Code Check-in**: Digital guest check-in system
- **Regional Support**: Designed for all 10 regions of Cameroon
- **Cultural Events**: Specialized tools for traditional ceremonies

## Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **shadcn/ui** - UI components
- **Recharts** - Data visualization

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MySQL** - Database
- **JWT** - Authentication
- **Multer** - File uploads
- **bcryptjs** - Password hashing

## Quick Start

### Prerequisites
- Node.js 18+ 
- MySQL 8.0+
- npm or yarn

### 1. <PERSON><PERSON> the Repository
\`\`\`bash
git clone https://github.com/Ngounou-Arlane/otantikEMS-jk
cd otantik-ems
\`\`\`

### 2. Setup Backend
\`\`\`bash
cd backend
npm install
cp .env.example .env
\`\`\`

Edit `.env` file with your database credentials:
\`\`\`env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=otantik_ems
JWT_SECRET=helloworldmynameisotantiknuna
\`\`\`

### 3. Setup Database
\`\`\`bash
# Create database and tables
npm run migrate

# Seed with sample data
npm run seed
\`\`\`

### 4. Setup Frontend
\`\`\`bash
cd ..
npm install
\`\`\`

### 5. Start Development Servers
\`\`\`bash
# Start both backend and frontend
npm run dev:full

# Or start separately:
# Backend (port 3001)
npm run dev:backend

# Frontend (port 3000)
npm run dev
\`\`\`

### 6. Access the Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

## Default Login Credentials

### Admin
- Email: `<EMAIL>`
- Password: `password`

### Event Planner
- Email: `<EMAIL>`
- Password: `password`

### Planner 2
- Email: `<EMAIL>`
- Password: `password`

## Project Structure

\`\`\`
otantik-ems/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin dashboard pages
│   ├── planner/           # Planner dashboard pages
│   ├── vendor/            # Vendor dashboard pages
│   ├── guest/             # Guest portal pages
│   └── page.tsx           # Landing/login page
├── components/            # Reusable UI components
├── lib/                   # Utilities and services
│   ├── services/          # API service classes
│   └── api.js            # API client
├── backend/               # Express.js backend
│   ├── database/          # SQL schema and seeds
│   ├── scripts/           # Database migration scripts
│   └── server.js          # Main server file
└── public/               # Static assets
\`\`\`

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration

### Events
- `GET /api/events` - Get all events
- `POST /api/events` - Create new event
- `GET /api/events/:id` - Get specific event
- `PUT /api/events/:id` - Update event

### Guests
- `GET /api/events/:eventId/guests` - Get event guests
- `POST /api/events/:eventId/guests` - Add guest
- `PUT /api/events/:eventId/guests/:guestId` - Update guest

### Vendors
- `GET /api/vendors` - Get all vendors
- `POST /api/vendors` - Create vendor
- `PUT /api/vendors/:id` - Update vendor

### WhatsApp Integration
- `POST /api/events/:eventId/send-invitations` - Send invitations
- `POST /api/events/:eventId/send-thank-you` - Send thank you messages

## Environment Variables

### Backend (.env)
\`\`\`env
# Database
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=otantik_ems

# JWT
JWT_SECRET=your-super-secret-jwt-key

# Server
PORT=3001
NODE_ENV=development

# WhatsApp (Optional)
WHATSAPP_API_URL=
WHATSAPP_ACCESS_TOKEN=
\`\`\`

### Frontend (.env.local)
\`\`\`env
NEXT_PUBLIC_API_URL=http://localhost:3001/api
\`\`\`

## Database Schema

The system uses MySQL with the following main tables:
- `users` - System users (admin, planners, vendors, guests)
- `events` - Event information
- `guests` - Event guest lists
- `vendors` - Service providers
- `event_vendors` - Event-vendor assignments
- `media` - Event photos/videos
- `notifications` - System notifications

## Features by Role

### Admin
- System-wide dashboard and analytics
- User management (planners, vendors, guests)
- Event oversight across all regions
- Vendor approval and management
- System settings and configuration

### Event Planner
- Event creation and management
- Guest list management and RSVP tracking
- Vendor coordination and assignment
- Invitation sending via WhatsApp
- Seating chart management
- QR code generation for guests
- Live check-in monitoring

### Vendor
- Assigned event dashboard
- Media upload for completed events
- Client communication tools
- Event gallery management
- Profile and portfolio management

### Guest
- Personal event dashboard
- RSVP management
- Event details and directions
- QR code for check-in
- Photo gallery access

## Cameroon Regional Support

The system supports all 10 regions of Cameroon:
- Centre Region (Yaoundé)
- Littoral Region (Douala)
- Northwest Region (Bamenda)
- Southwest Region (Buea)
- West Region (Bafoussam)
- East Region (Bertoua)
- North Region (Garoua)
- Adamawa Region (Ngaoundéré)
- Far North Region (Maroua)
- South Region (Ebolowa)

## WhatsApp Integration

The system includes WhatsApp Business API integration for:
- Sending event invitations with custom messages
- RSVP reminders
- Thank you messages post-event
- Event updates and notifications

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request
