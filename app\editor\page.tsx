"use client"

import type React from "react"
import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Slider } from "@/components/ui/slider"
import { Separator } from "@/components/ui/separator"
import {
  Type,
  ImageIcon,
  Square,
  Circle,
  Download,
  Share2,
  Undo,
  Redo,
  Trash2,
  Copy,
  Eye,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Palette,
  Save,
  FileImage,
  Layers,
  Move,
  Sparkles,
} from "lucide-react"
import Link from "next/link"
import html2canvas from "html2canvas"
import jsPDF from "jspdf"

interface DesignElement {
  id: string
  type: "text" | "image" | "shape"
  x: number
  y: number
  width: number
  height: number
  rotation: number
  opacity: number
  zIndex: number

  // Text properties
  content?: string
  fontSize?: number
  fontFamily?: string
  color?: string
  textAlign?: "left" | "center" | "right"
  fontWeight?: "normal" | "bold"
  fontStyle?: "normal" | "italic"
  textDecoration?: "none" | "underline" | "line-through"

  // Image properties
  src?: string
  filter?: string

  // Shape properties
  backgroundColor?: string
  borderRadius?: number
  shapeType?: "rectangle" | "circle"
}

// Template definitions with actual design elements
const templateData = {
  5: {
    name: "Birthday Bash",
    category: "birthday",
    style: "fun",
    popular: true,
    background: "url('/images/hbd/1.png') center/cover no-repeat",
    elements: [],
  },
  1: {
    name: "Elegant Wedding",
    category: "wedding",
    style: "elegant",
    popular: true,
    background: "url('/images/wedding/1.png') center/cover no-repeat",
    elements: [],
  },
  9: {
    name: "Conference 2024",
    category: "corporate",
    style: "professional",
    popular: true,
    background: "url('/images/conference/1.png') center/cover no-repeat",
    elements: [],
  },
  14: {
    name: "Holiday Cheer",
    category: "holiday",
    style: "festive",
    popular: true,
    background: "url('/images/holiday/1.png') center/cover no-repeat",
    elements: [],
  },
  7: {
    name: "Baby Shower Joy",
    category: "baby",
    style: "cute",
    popular: true,
    background: "url('/images/babyShower/1.png') center/cover no-repeat",
    elements: [],
  },
  2: {
    name: "Classic Wedding",
    category: "wedding",
    style: "classic",
    popular: false,
    background: "url('/images/wedding/2.png') center/cover no-repeat",
    elements: [],
  },
  6: {
    name: "Birthday Surprise",
    category: "birthday",
    style: "colorful",
    popular: false,
    background: "url('/images/hbd/2.png') center/cover no-repeat",
    elements: [],
  },
  10: {
    name: "Tech Meetup",
    category: "corporate",
    style: "modern",
    popular: false,
    background: "url('/images/conference/2.png') center/cover no-repeat",
    elements: [],
  },
  3: {
    name: "Modern Wedding",
    category: "wedding",
    style: "modern",
    popular: false,
    background: "url('/images/wedding/3.png') center/cover no-repeat",
    elements: [],
  },
  8: {
    name: "Little One's Shower",
    category: "baby",
    style: "playful",
    popular: false,
    background: "url('/images/babyShower/2.png') center/cover no-repeat",
    elements: [],
  },
  4: {
    name: "Romantic Wedding",
    category: "wedding",
    style: "romantic",
    popular: false,
    background: "url('/images/wedding/4.png') center/cover no-repeat",
    elements: [],
  },
  11: {
    name: "Graduation Gala",
    category: "graduation",
    style: "modern",
    popular: false,
    background: "url('/images/graduation/1.png') center/cover no-repeat",
    elements: [],
  },
  12: {
    name: "Cap & Gown Party",
    category: "graduation",
    style: "classic",
    popular: false,
    background: "url('/images/graduation/2.png') center/cover no-repeat",
    elements: [],
  },
  13: {
    name: "Graduation Celebration",
    category: "graduation",
    style: "festive",
    popular: false,
    background: "url('/images/graduation/3.png') center/cover no-repeat",
    elements: [],
  },
  15: {
    name: "Festive Gathering",
    category: "holiday",
    style: "classic",
    popular: false,
    background: "url('/images/holiday/2.png') center/cover no-repeat",
    elements: [],
  },
}

const EditorPageComponent: React.FC = () => {
  const [elements, setElements] = useState<DesignElement[]>([])
  const [selectedElement, setSelectedElement] = useState<string | null>(null)
  const [canvasBackground, setCanvasBackground] = useState("#FFFFFF")
  const [backgroundType, setBackgroundType] = useState("solid")
  const [gradientColors, setGradientColors] = useState(["#FFFFFF", "#F0F0F0"])
  const [showPreview, setShowPreview] = useState(false)
  const [draggedElement, setDraggedElement] = useState<string | null>(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [history, setHistory] = useState<DesignElement[][]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)

  const canvasRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const saveToHistory = useCallback(
    (newElements: DesignElement[]) => {
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(JSON.parse(JSON.stringify(newElements)))
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)
    },
    [history, historyIndex],
  )

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setElements(JSON.parse(JSON.stringify(history[historyIndex - 1])))
    }
  }

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setElements(JSON.parse(JSON.stringify(history[historyIndex + 1])))
    }
  }

  const addTextElement = () => {
    const newElement: DesignElement = {
      id: Date.now().toString(),
      type: "text",
      x: 100,
      y: 100,
      width: 200,
      height: 40,
      rotation: 0,
      opacity: 1,
      zIndex: elements.length + 1,
      content: "New Text",
      fontSize: 16,
      fontFamily: "Arial",
      color: "#000000",
      textAlign: "left",
      fontWeight: "normal",
      fontStyle: "normal",
      textDecoration: "none",
    }
    const newElements = [...elements, newElement]
    setElements(newElements)
    setSelectedElement(newElement.id)
    saveToHistory(newElements)
  }

  const addShapeElement = (shapeType: "rectangle" | "circle") => {
    const newElement: DesignElement = {
      id: Date.now().toString(),
      type: "shape",
      x: 100,
      y: 100,
      width: 100,
      height: 100,
      rotation: 0,
      opacity: 1,
      zIndex: elements.length + 1,
      backgroundColor: "#3182CE",
      shapeType,
      borderRadius: shapeType === "circle" ? 50 : 0,
    }
    const newElements = [...elements, newElement]
    setElements(newElements)
    setSelectedElement(newElement.id)
    saveToHistory(newElements)
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const newElement: DesignElement = {
          id: Date.now().toString(),
          type: "image",
          x: 100,
          y: 100,
          width: 150,
          height: 150,
          rotation: 0,
          opacity: 1,
          zIndex: elements.length + 1,
          src: e.target?.result as string,
          filter: "none",
        }
        const newElements = [...elements, newElement]
        setElements(newElements)
        setSelectedElement(newElement.id)
        saveToHistory(newElements)
      }
      reader.readAsDataURL(file)
    }
  }

  const updateElement = (id: string, updates: Partial<DesignElement>) => {
    const newElements = elements.map((el) => (el.id === id ? { ...el, ...updates } : el))
    setElements(newElements)
    saveToHistory(newElements)
  }

  const deleteElement = (id: string) => {
    const newElements = elements.filter((el) => el.id !== id)
    setElements(newElements)
    setSelectedElement(null)
    saveToHistory(newElements)
  }

  const duplicateElement = (id: string) => {
    const element = elements.find((el) => el.id === id)
    if (element) {
      const newElement = {
        ...element,
        id: Date.now().toString(),
        x: element.x + 20,
        y: element.y + 20,
        zIndex: elements.length + 1,
      }
      const newElements = [...elements, newElement]
      setElements(newElements)
      setSelectedElement(newElement.id)
      saveToHistory(newElements)
    }
  }

  const moveElementUp = (id: string) => {
    const element = elements.find((el) => el.id === id)
    if (element) {
      updateElement(id, { zIndex: element.zIndex + 1 })
    }
  }

  const moveElementDown = (id: string) => {
    const element = elements.find((el) => el.id === id)
    if (element && element.zIndex > 1) {
      updateElement(id, { zIndex: element.zIndex - 1 })
    }
  }

  const handleMouseDown = (e: React.MouseEvent, elementId: string) => {
    e.preventDefault()
    setSelectedElement(elementId)
    setDraggedElement(elementId)

    const element = elements.find((el) => el.id === elementId)
    if (element) {
      const rect = canvasRef.current?.getBoundingClientRect()
      if (rect) {
        setDragOffset({
          x: e.clientX - rect.left - element.x,
          y: e.clientY - rect.top - element.y,
        })
      }
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (draggedElement) {
      const rect = canvasRef.current?.getBoundingClientRect()
      if (rect) {
        const newX = e.clientX - rect.left - dragOffset.x
        const newY = e.clientY - rect.top - dragOffset.y

        updateElement(draggedElement, { x: newX, y: newY })
      }
    }
  }

  const handleMouseUp = () => {
    setDraggedElement(null)
  }

  const getBackgroundStyle = () => {
    switch (backgroundType) {
      case "linear":
        return `linear-gradient(135deg, ${gradientColors[0]}, ${gradientColors[1]})`
      case "radial":
        return `radial-gradient(circle, ${gradientColors[0]}, ${gradientColors[1]})`
      case "dots":
        return `radial-gradient(circle, ${canvasBackground} 1px, transparent 1px)`
      case "lines":
        return `repeating-linear-gradient(45deg, ${canvasBackground}, ${canvasBackground} 10px, transparent 10px, transparent 20px)`
      default:
        return canvasBackground
    }
  }

  const exportAsImage = async (format: string) => {
    if (!canvasRef.current) return
    const canvasElement = canvasRef.current as HTMLDivElement
    const scale = 2
    const width = 400
    const height = 600

    // Use html2canvas to render the canvas
    const canvas = await html2canvas(canvasElement, {
      backgroundColor: null,
      scale,
      width,
      height,
      useCORS: true,
    })

    if (format === "png") {
      const dataUrl = canvas.toDataURL("image/png")
      const link = document.createElement("a")
      link.href = dataUrl
      link.download = "invitation.png"
      link.click()
    } else if (format === "pdf") {
      const imgData = canvas.toDataURL("image/png")
      const pdf = new jsPDF({ orientation: "portrait", unit: "px", format: [width, height] })
      pdf.addImage(imgData, "PNG", 0, 0, width, height)
      pdf.save("invitation.pdf")
    }
  }

  const handleSave = () => {
    // Save the current design to localStorage (can be replaced with API call)
    const design = {
      elements,
      background: canvasBackground,
      backgroundType,
      gradientColors,
    }
    localStorage.setItem("invitation-design", JSON.stringify(design))
    alert("Design saved locally!")
  }

  const selectedElementData = elements.find((el) => el.id === selectedElement)
  const sortedElements = [...elements].sort((a, b) => a.zIndex - b.zIndex)

  const backgroundPatterns = [
    { name: "Solid", value: "solid" },
    { name: "Linear Gradient", value: "linear" },
    { name: "Radial Gradient", value: "radial" },
    { name: "Dots", value: "dots" },
    { name: "Lines", value: "lines" },
  ]

  const colors = [
    "#FFFFFF",
    "#000000",
    "#F44336",
    "#E91E63",
    "#9C27B0",
    "#673AB7",
    "#3F51B5",
    "#2196F3",
    "#03A9F4",
    "#00BCD4",
    "#009688",
    "#4CAF50",
    "#8BC34A",
    "#CDDC39",
    "#FFEB3B",
    "#FFC107",
    "#FF9800",
    "#FF5722",
    "#795548",
    "#9E9E9E",
    "#607D8B",
  ]

  const imageFilters = [
    { name: "None", value: "none" },
    { name: "Grayscale", value: "grayscale(100%)" },
    { name: "Sepia", value: "sepia(100%)" },
    { name: "Blur", value: "blur(5px)" },
    { name: "Invert", value: "invert(100%)" },
    { name: "Saturate", value: "saturate(200%)" },
    { name: "Contrast", value: "contrast(150%)" },
  ]

  const fonts = [
    "Arial",
    "Helvetica",
    "Times New Roman",
    "Courier New",
    "Georgia",
    "Verdana",
    "Impact",
    "Comic Sans MS",
    "Trebuchet MS",
    "Arial Black",
  ]

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const templateId = urlParams.get("template")

    if (templateId && templateData[templateId as keyof typeof templateData]) {
      const template = templateData[templateId as keyof typeof templateData]
      setElements(template.elements)
      setCanvasBackground(template.background)
      setBackgroundType(template.background.includes("gradient") ? "linear" : "solid")

      // Extract gradient colors if it's a gradient
      if (template.background.includes("gradient")) {
        const colorMatches = template.background.match(/#[a-fA-F0-9]{6}/g)
        if (colorMatches && colorMatches.length >= 2) {
          setGradientColors([colorMatches[0], colorMatches[1]])
        }
      }
    } else {
      // Default single text element for blank canvas
      setElements([
        {
          id: "1",
          type: "text",
          x: 50,
          y: 50,
          width: 300,
          height: 60,
          rotation: 0,
          opacity: 1,
          zIndex: 1,
          content: "You're Invited!",
          fontSize: 32,
          fontFamily: "Arial",
          color: "#2D3748",
          textAlign: "center",
          fontWeight: "bold",
          fontStyle: "normal",
          textDecoration: "none",
        },
      ])
    }
  }, [])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-full mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2">
                <Sparkles className="w-6 h-6 text-purple-600" />
                <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  InviteDesigner
                </span>
              </Link>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" onClick={undo} disabled={historyIndex <= 0}>
                  <Undo className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={redo} disabled={historyIndex >= history.length - 1}>
                  <Redo className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={() => setShowPreview(!showPreview)}>
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </Button>
              <Button variant="outline" onClick={() => exportAsImage("png")}>
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
              <Button variant="outline" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
              <Button className="bg-gradient-to-r from-purple-600 to-pink-600">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Left Sidebar */}
        <div className="w-80 bg-white border-r overflow-y-auto">
          <div className="p-4">
            <Tabs defaultValue="elements" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="elements">Elements</TabsTrigger>
                <TabsTrigger value="design">Design</TabsTrigger>
                <TabsTrigger value="text">Text</TabsTrigger>
                <TabsTrigger value="layers">Layers</TabsTrigger>
              </TabsList>

              <TabsContent value="elements" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center">
                      <Type className="w-4 h-4 mr-2" />
                      Add Elements
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <Button onClick={addTextElement} variant="outline" className="w-full justify-start bg-transparent">
                      <Type className="w-4 h-4 mr-2" />
                      Add Text
                    </Button>

                    <div className="relative">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      />
                      <Button variant="outline" className="w-full justify-start bg-transparent">
                        <ImageIcon className="w-4 h-4 mr-2" />
                        Upload Image
                      </Button>
                    </div>

                    <Button
                      onClick={() => addShapeElement("rectangle")}
                      variant="outline"
                      className="w-full justify-start"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Rectangle
                    </Button>

                    <Button
                      onClick={() => addShapeElement("circle")}
                      variant="outline"
                      className="w-full justify-start"
                    >
                      <Circle className="w-4 h-4 mr-2" />
                      Circle
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="design" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center">
                      <Palette className="w-4 h-4 mr-2" />
                      Background
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Background Type</Label>
                      <Select value={backgroundType} onValueChange={setBackgroundType}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {backgroundPatterns.map((pattern) => (
                            <SelectItem key={pattern.value} value={pattern.value}>
                              {pattern.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Colors</Label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {colors.map((color) => (
                          <button
                            key={color}
                            className="w-8 h-8 rounded border-2 border-gray-300 hover:scale-110 transition-transform"
                            style={{ backgroundColor: color }}
                            onClick={() => setCanvasBackground(color)}
                          />
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {selectedElementData && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center">
                        <Move className="w-4 h-4 mr-2" />
                        Element Properties
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label>X Position</Label>
                          <Input
                            type="number"
                            value={selectedElementData.x}
                            onChange={(e) =>
                              updateElement(selectedElementData.id, { x: Number.parseInt(e.target.value) })
                            }
                          />
                        </div>
                        <div>
                          <Label>Y Position</Label>
                          <Input
                            type="number"
                            value={selectedElementData.y}
                            onChange={(e) =>
                              updateElement(selectedElementData.id, { y: Number.parseInt(e.target.value) })
                            }
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label>Width</Label>
                          <Input
                            type="number"
                            value={selectedElementData.width}
                            onChange={(e) =>
                              updateElement(selectedElementData.id, { width: Number.parseInt(e.target.value) })
                            }
                          />
                        </div>
                        <div>
                          <Label>Height</Label>
                          <Input
                            type="number"
                            value={selectedElementData.height}
                            onChange={(e) =>
                              updateElement(selectedElementData.id, { height: Number.parseInt(e.target.value) })
                            }
                          />
                        </div>
                      </div>

                      <div>
                        <Label>Rotation</Label>
                        <Slider
                          value={[selectedElementData.rotation]}
                          onValueChange={([value]) => updateElement(selectedElementData.id, { rotation: value })}
                          max={360}
                          min={0}
                          step={1}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Opacity</Label>
                        <Slider
                          value={[selectedElementData.opacity]}
                          onValueChange={([value]) => updateElement(selectedElementData.id, { opacity: value })}
                          max={1}
                          min={0}
                          step={0.1}
                          className="mt-2"
                        />
                      </div>

                      {selectedElementData.type === "image" && (
                        <div>
                          <Label>Filter</Label>
                          <Select
                            value={selectedElementData.filter || "none"}
                            onValueChange={(value) => updateElement(selectedElementData.id, { filter: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {imageFilters.map((filter) => (
                                <SelectItem key={filter.value} value={filter.value}>
                                  {filter.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="text" className="space-y-4">
                {selectedElementData?.type === "text" && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center">
                        <Type className="w-4 h-4 mr-2" />
                        Text Properties
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label>Text Content</Label>
                        <Input
                          value={selectedElementData.content || ""}
                          onChange={(e) => updateElement(selectedElementData.id, { content: e.target.value })}
                          placeholder="Enter text..."
                        />
                      </div>

                      <div>
                        <Label>Font Family</Label>
                        <Select
                          value={selectedElementData.fontFamily}
                          onValueChange={(value) => updateElement(selectedElementData.id, { fontFamily: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {fonts.map((font) => (
                              <SelectItem key={font} value={font}>
                                {font}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Font Size</Label>
                        <Slider
                          value={[selectedElementData.fontSize || 16]}
                          onValueChange={([value]) => updateElement(selectedElementData.id, { fontSize: value })}
                          max={72}
                          min={8}
                          step={1}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Text Color</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {colors.map((color) => (
                            <button
                              key={color}
                              className="w-8 h-8 rounded border-2 border-gray-300 hover:scale-110 transition-transform"
                              style={{ backgroundColor: color }}
                              onClick={() => updateElement(selectedElementData.id, { color })}
                            />
                          ))}
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          variant={selectedElementData.textAlign === "left" ? "default" : "outline"}
                          size="sm"
                          onClick={() => updateElement(selectedElementData.id, { textAlign: "left" })}
                        >
                          <AlignLeft className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={selectedElementData.textAlign === "center" ? "default" : "outline"}
                          size="sm"
                          onClick={() => updateElement(selectedElementData.id, { textAlign: "center" })}
                        >
                          <AlignCenter className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={selectedElementData.textAlign === "right" ? "default" : "outline"}
                          size="sm"
                          onClick={() => updateElement(selectedElementData.id, { textAlign: "right" })}
                        >
                          <AlignRight className="w-4 h-4" />
                        </Button>
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          variant={selectedElementData.fontWeight === "bold" ? "default" : "outline"}
                          size="sm"
                          onClick={() =>
                            updateElement(selectedElementData.id, {
                              fontWeight: selectedElementData.fontWeight === "bold" ? "normal" : "bold",
                            })
                          }
                        >
                          <Bold className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={selectedElementData.fontStyle === "italic" ? "default" : "outline"}
                          size="sm"
                          onClick={() =>
                            updateElement(selectedElementData.id, {
                              fontStyle: selectedElementData.fontStyle === "italic" ? "normal" : "italic",
                            })
                          }
                        >
                          <Italic className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={selectedElementData.textDecoration === "underline" ? "default" : "outline"}
                          size="sm"
                          onClick={() =>
                            updateElement(selectedElementData.id, {
                              textDecoration: selectedElementData.textDecoration === "underline" ? "none" : "underline",
                            })
                          }
                        >
                          <Underline className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={selectedElementData.textDecoration === "line-through" ? "default" : "outline"}
                          size="sm"
                          onClick={() =>
                            updateElement(selectedElementData.id, {
                              textDecoration:
                                selectedElementData.textDecoration === "line-through" ? "none" : "line-through",
                            })
                          }
                        >
                          <Strikethrough className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="layers" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center">
                      <Layers className="w-4 h-4 mr-2" />
                      Layer Management
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {elements
                      .sort((a, b) => b.zIndex - a.zIndex)
                      .map((element, index) => (
                        <div
                          key={element.id}
                          className={`flex items-center justify-between p-2 rounded cursor-pointer ${
                            selectedElement === element.id ? "bg-blue-100" : "hover:bg-gray-100"
                          }`}
                          onClick={() => setSelectedElement(element.id)}
                        >
                          <span className="text-sm capitalize">
                            {element.type} {elements.length - index}
                          </span>
                          <div className="flex space-x-1">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation()
                                moveElementUp(element.id)
                              }}
                            >
                              ↑
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation()
                                moveElementDown(element.id)
                              }}
                            >
                              ↓
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation()
                                duplicateElement(element.id)
                              }}
                            >
                              <Copy className="w-3 h-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation()
                                deleteElement(element.id)
                              }}
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Main Canvas Area */}
        <div className="flex-1 flex items-center justify-center p-8 bg-gray-100">
          <div className="bg-white shadow-2xl rounded-lg overflow-hidden">
            <div
              ref={canvasRef}
              className="relative cursor-crosshair"
              style={{
                width: 400,
                height: 600,
                background: getBackgroundStyle(),
              }}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            >
              {sortedElements.map((element) => (
                <div
                  key={element.id}
                  className={`absolute cursor-move select-none ${
                    selectedElement === element.id ? "ring-2 ring-blue-500 ring-dashed" : ""
                  }`}
                  style={{
                    left: element.x,
                    top: element.y,
                    width: element.width,
                    height: element.height,
                    transform: `rotate(${element.rotation}deg)`,
                    opacity: element.opacity,
                    zIndex: element.zIndex,
                  }}
                  onMouseDown={(e) => handleMouseDown(e, element.id)}
                >
                  {element.type === "text" && (
                    <div
                      className="w-full h-full flex items-center justify-center p-2"
                      style={{
                        fontSize: element.fontSize,
                        fontFamily: element.fontFamily,
                        color: element.color,
                        textAlign: element.textAlign,
                        fontWeight: element.fontWeight,
                        fontStyle: element.fontStyle,
                        textDecoration: element.textDecoration,
                      }}
                    >
                      {element.content}
                    </div>
                  )}

                  {element.type === "image" && element.src && (
                    <img
                      src={element.src || "/placeholder.svg"}
                      alt="Uploaded"
                      className="w-full h-full object-cover"
                      style={{
                        filter: element.filter === "none" ? undefined : element.filter,
                      }}
                      draggable={false}
                    />
                  )}

                  {element.type === "shape" && (
                    <div
                      className="w-full h-full"
                      style={{
                        backgroundColor: element.backgroundColor,
                        borderRadius: element.shapeType === "circle" ? "50%" : element.borderRadius,
                      }}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-2xl max-h-[90vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Preview</h3>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => exportAsImage("png")}>
                  <FileImage className="w-4 h-4 mr-2" />
                  PNG
                </Button>
                <Button variant="outline" onClick={() => exportAsImage("pdf")}>
                  <Download className="w-4 h-4 mr-2" />
                  PDF
                </Button>
                <Button variant="ghost" onClick={() => setShowPreview(false)}>
                  ×
                </Button>
              </div>
            </div>
            <div
              className="mx-auto border rounded shadow-lg relative"
              style={{
                width: 400,
                height: 600,
                background: getBackgroundStyle(),
              }}
            >
              {sortedElements.map((element) => (
                <div
                  key={element.id}
                  className="absolute"
                  style={{
                    left: element.x,
                    top: element.y,
                    width: element.width,
                    height: element.height,
                    transform: `rotate(${element.rotation}deg)`,
                    opacity: element.opacity,
                    zIndex: element.zIndex,
                  }}
                >
                  {element.type === "text" && (
                    <div
                      className="w-full h-full flex items-center justify-center p-2"
                      style={{
                        fontSize: element.fontSize,
                        fontFamily: element.fontFamily,
                        color: element.color,
                        textAlign: element.textAlign,
                        fontWeight: element.fontWeight,
                        fontStyle: element.fontStyle,
                        textDecoration: element.textDecoration,
                      }}
                    >
                      {element.content}
                    </div>
                  )}

                  {element.type === "image" && element.src && (
                    <img
                      src={element.src || "/placeholder.svg"}
                      alt="Uploaded"
                      className="w-full h-full object-cover"
                      style={{
                        filter: element.filter === "none" ? undefined : element.filter,
                      }}
                    />
                  )}

                  {element.type === "shape" && (
                    <div
                      className="w-full h-full"
                      style={{
                        backgroundColor: element.backgroundColor,
                        borderRadius: element.shapeType === "circle" ? "50%" : element.borderRadius,
                      }}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EditorPageComponent
