import apiClient from "@/lib/api"

export interface User {
  id: number
  email: string
  role: string
  firstName: string
  lastName: string
  region: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  firstName: string
  lastName: string
  email: string
  password: string
  role: string
  region: string
  phone?: string
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<{ token: string; user: User }> {
    const response = await apiClient.request("/auth/login", {
      method: "POST",
      body: credentials,
    })

    if (response.token) {
      apiClient.setToken(response.token)
      localStorage.setItem("user", JSON.stringify(response.user))
    }

    return response
  }

  async register(userData: RegisterData): Promise<{ token: string; user: User }> {
    const response = await apiClient.request("/auth/register", {
      method: "POST",
      body: userData,
    })

    if (response.token) {
      apiClient.setToken(response.token)
      localStorage.setItem("user", JSON.stringify(response.user))
    }

    return response
  }

  logout(): void {
    apiClient.setToken(null)
    localStorage.removeItem("user")
  }

  getCurrentUser(): User | null {
    if (typeof window === "undefined") return null

    const userStr = localStorage.getItem("user")
    return userStr ? JSON.parse(userStr) : null
  }

  isAuthenticated(): boolean {
    return !!this.getCurrentUser()
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser()
    return user?.role === role
  }
}

export const authService = new AuthService()
