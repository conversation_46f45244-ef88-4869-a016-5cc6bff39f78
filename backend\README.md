# OtantikEMS Backend

A comprehensive Event Management System backend built with Node.js, Express, and MySQL.

## Features

- 🔐 **JWT Authentication** with role-based access control
- 👥 **User Management** (Planners, Vendors, Guests)
- 🎉 **Event Management** with full CRUD operations
- 📱 **WhatsApp Integration** for invitations and notifications
- 📸 **File Upload** system for media management
- 🎫 **QR Code Generation** for guest check-ins
- 📊 **Analytics Dashboard** with real-time statistics
- 🔔 **Notification System** for real-time updates

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- npm or yarn

### Installation

1. **Clone and setup**
   \`\`\`bash
   cd backend
   npm install
   \`\`\`

2. **Environment Configuration**
   \`\`\`bash
   cp .env.example .env
   # Edit .env with your database and API credentials
   \`\`\`

3. **Database Setup**
   \`\`\`bash
   # Create database and tables
   npm run migrate
   
   # Insert sample data
   npm run seed
   
   # Or run both commands
   npm run setup
   \`\`\`

4. **Start Development Server**
   \`\`\`bash
   npm run dev
   \`\`\`

The server will start on `http://localhost:3001`

## Environment Variables

\`\`\`env
# Database
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=otantik_ems

# Security
JWT_SECRET=your_jwt_secret_key

# WhatsApp (Optional)
WHATSAPP_ACCESS_TOKEN=your_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_id
WHATSAPP_API_URL=https://graph.facebook.com/v18.0

# Frontend
FRONTEND_URL=http://localhost:3000
\`\`\`

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration

### Events
- `GET /api/events` - Get all events
- `POST /api/events` - Create new event
- `GET /api/events/:id` - Get specific event
- `PUT /api/events/:id` - Update event

### Users (Planner only)
- `GET /api/users` - Get all users
- `POST /api/users` - Create new user

### Vendors
- `GET /api/vendors` - Get all vendors
- `POST /api/vendors` - Create new vendor

### Guests
- `GET /api/events/:eventId/guests` - Get event guests
- `POST /api/events/:eventId/guests` - Add guest to event

### WhatsApp
- `POST /api/events/:eventId/send-invitations` - Send invitations
- `POST /api/events/:eventId/send-thank-you` - Send thank you messages

### QR Codes
- `GET /api/events/:eventId/guests/:guestId/qr` - Generate QR code
- `POST /api/checkin` - Guest check-in via QR

### File Upload
- `POST /api/upload` - Upload files/media

### Dashboard
- `GET /api/dashboard/stats` - Get dashboard statistics

### Notifications
- `GET /api/notifications` - Get user notifications

## Default Login Credentials

\`\`\`
Planner: <EMAIL> / password123
Vendor: <EMAIL> / password123
Guest: <EMAIL> / password123
\`\`\`

## Database Schema

The system uses the following main tables:
- `users` - User accounts and authentication
- `events` - Event information and details
- `vendors` - Vendor profiles and services
- `guests` - Event guest lists and RSVP status
- `event_vendors` - Many-to-many relationship between events and vendors
- `media` - File uploads and media management
- `notifications` - System notifications
- `whatsapp_messages` - WhatsApp message logs

## Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run migrate` - Run database migrations
- `npm run seed` - Insert sample data
- `npm run setup` - Run migrations and seeding
- `npm test` - Run tests

## WhatsApp Integration

The system supports WhatsApp Business API for:
- Sending event invitations
- Thank you messages after events
- Event reminders and updates

Configure your WhatsApp credentials in the `.env` file to enable messaging features.

## File Upload

Supports uploading:
- Images (JPEG, PNG, GIF)
- Documents (PDF, DOC, DOCX)
- Maximum file size: 10MB

Files are stored in the `uploads/` directory and served statically.

## Security Features

- JWT token authentication
- Password hashing with bcrypt
- Role-based access control
- Input validation and sanitization
- CORS protection
- Helmet security headers

## Development

For development, the server uses nodemon for auto-reloading:

\`\`\`bash
npm run dev
\`\`\`

The API will be available at `http://localhost:3001/api`

## Production Deployment

1. Set `NODE_ENV=production` in your environment
2. Configure production database credentials
3. Set up proper SSL certificates
4. Use a process manager like PM2
5. Configure reverse proxy (nginx/Apache)

## Support

For issues and questions, please check the documentation or contact the development team.
\`\`\`

Now let's create the frontend services and components:
