"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, ImageIcon, Link, Copy, Check, Eye, Loader2, Camera, Share2, FolderOpen } from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"
import apiClient from "@/lib/api"
import { toast } from "sonner"

interface Event {
  id: number
  title: string
  event_date: string
}

interface PhotoObject {
  id: number
  original_name: string
  file_size?: number
  file_url: string
  caption?: string
  uploaded_at?: string
}

interface UploadedPhoto {
  id: number
  name: string
  size: number
  url: string
  caption: string
  uploaded: boolean
}

export default function PhotoUploadPage() {
  const [events, setEvents] = useState<Event[]>([])
  const [selectedEvent, setSelectedEvent] = useState("")
  const [uploadedPhotos, setUploadedPhotos] = useState<UploadedPhoto[]>([])
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [galleryLink, setGalleryLink] = useState("")
  const [isGeneratingLink, setIsGeneratingLink] = useState(false)
  const [dragActive, setDragActive] = useState(false)

  useEffect(() => {
    loadEvents()
  }, [])

  useEffect(() => {
    if (selectedEvent) {
      loadExistingPhotos()
      generateGalleryLink()
    }
  }, [selectedEvent])

  const loadEvents = async () => {
    try {
      const eventsData = await apiClient.getEvents()
      setEvents(eventsData)
      if (eventsData.length > 0) {
        setSelectedEvent(eventsData[0].id.toString())
      }
    } catch (error) {
      console.error("Failed to load events:", error)
      toast.error("Failed to load events")
    } finally {
      setIsLoading(false)
    }
  }

  const loadExistingPhotos = async () => {
    if (!selectedEvent) return

    try {
      const photos: PhotoObject[] = await apiClient.getEventPhotos(parseInt(selectedEvent))
      const formattedPhotos = photos.map((photo: PhotoObject) => ({
        id: photo.id,
        name: photo.original_name,
        size: photo.file_size || 0,
        url: photo.file_url,
        caption: photo.caption || "",
        uploaded: true,
      }))
      setUploadedPhotos(formattedPhotos)
    } catch (error) {
      console.error("Failed to load existing photos:", error)
    }
  }

  const generateGalleryLink = async () => {
    if (!selectedEvent) return

    setIsGeneratingLink(true)
    try {
      const link = await apiClient.generateEventGalleryLink(parseInt(selectedEvent))
      setGalleryLink(link)
    } catch (error) {
      console.error("Failed to generate gallery link:", error)
      // Generate a fallback link
      setGalleryLink(`${window.location.origin}/gallery/${selectedEvent}`)
    } finally {
      setIsGeneratingLink(false)
    }
  }

  const handlePhotoUpload = async (files: FileList | null) => {
    if (!files || !selectedEvent) return

    setUploading(true)
    setUploadProgress(0)

    try {
      const fileArray = Array.from(files)
      const totalFiles = fileArray.length
      let completedFiles = 0

      const uploadPromises = fileArray.map(async (file) => {
        try {
          const result = await apiClient.uploadEventPhoto(file, parseInt(selectedEvent), `Event photo - ${file.name}`)

          const newPhoto = {
            id: result.id,
            name: file.name,
            size: file.size,
            url: result.fileUrl,
            caption: "",
            uploaded: true,
          }

          completedFiles++
          setUploadProgress((completedFiles / totalFiles) * 100)

          return newPhoto
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error)
          toast.error(`Failed to upload ${file.name}`)
          return null
        }
      })

      const results = await Promise.all(uploadPromises)
      const successfulUploads = results.filter((result) => result !== null)

      setUploadedPhotos((prev) => [...prev, ...successfulUploads])

      if (successfulUploads.length > 0) {
        toast.success(`Successfully uploaded ${successfulUploads.length} photo(s)`)
        // Regenerate gallery link after upload
        generateGalleryLink()
      }
    } catch (error) {
      console.error("Upload error:", error)
      toast.error("Failed to upload photos")
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handlePhotoUpload(e.dataTransfer.files)
    }
  }

  const openFileExplorer = () => {
    const input = document.getElementById("photo-upload") as HTMLInputElement
    if (input) {
      input.click()
    }
  }

  const removePhoto = async (id: number) => {
    try {
      await apiClient.deleteEventPhoto(id)
      setUploadedPhotos((prev) => prev.filter((photo) => photo.id !== id))
      toast.success("Photo removed successfully")
    } catch (error) {
      console.error("Failed to remove photo:", error)
      toast.error("Failed to remove photo")
    }
  }

  const updatePhotoCaption = async (photoId: number, caption: string) => {
    try {
      await apiClient.updateEventPhoto(photoId, { caption })
      setUploadedPhotos((prev) => prev.map((photo) => (photo.id === photoId ? { ...photo, caption } : photo)))
      toast.success("Caption updated successfully")
    } catch (error) {
      console.error("Failed to update caption:", error)
      toast.error("Failed to update caption")
    }
  }

  const copyGalleryLink = async () => {
    try {
      await navigator.clipboard.writeText(galleryLink)
      toast.success("Gallery link copied to clipboard!")
    } catch (error) {
      console.error("Failed to copy link:", error)
      toast.error("Failed to copy gallery link")
    }
  }

  const includeLinkInThankYou = async () => {
    if (!selectedEvent) {
      toast.error("Please select an event")
      return
    }

    try {
      const thankYouMessage = `Dear {name},

Thank you so much for being part of our special day! Your presence made our celebration truly memorable.

We are grateful for the love, joy, and wonderful memories you helped create.

📸 Event Photos: ${galleryLink}

With heartfelt appreciation,
The Event Team

#OtantikEMS`

      const result = await apiClient.sendThankYouMessages(parseInt(selectedEvent), thankYouMessage)
      
      if (result.success) {
        toast.success(`Thank you messages with photo link sent to ${result.sentCount} guests!`)
      } else {
        toast.error(result.error || "Failed to send thank you messages")
      }
    } catch (error) {
      console.error("Failed to send thank you messages:", error)
      toast.error("Failed to send thank you messages")
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <PlannerSidebar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading events...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Event Photo Upload</h1>
              <p className="text-gray-600">Upload event photos and share them with your guests</p>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Event Selection */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Select Event</CardTitle>
              <CardDescription>Choose the event to upload photos for</CardDescription>
            </CardHeader>
            <CardContent>
              <Select value={selectedEvent} onValueChange={setSelectedEvent}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose an event" />
                </SelectTrigger>
                <SelectContent>
                  {events.map((event) => (
                    <SelectItem key={event.id} value={event.id.toString()}>
                      {event.title} - {new Date(event.event_date).toLocaleDateString()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Gallery Link */}
          {selectedEvent && (
            <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Link className="h-5 w-5" />
                  Gallery Link
                </CardTitle>
                <CardDescription>
                  Share this link with your guests to view event photos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Input
                      value={galleryLink}
                      readOnly
                      placeholder={isGeneratingLink ? "Generating link..." : "Gallery link will appear here"}
                    />
                  </div>
                  <Button
                    onClick={copyGalleryLink}
                    disabled={!galleryLink || isGeneratingLink}
                    variant="outline"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Link
                  </Button>
                  <Button
                    onClick={includeLinkInThankYou}
                    disabled={!galleryLink || isGeneratingLink}
                    className="bg-gradient-to-r from-green-600 to-emerald-600"
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Send with Thank You
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Upload Area */}
          {selectedEvent && (
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle>Upload Event Photos</CardTitle>
                <CardDescription>
                  Upload photos from your event. These will be available to guests via the gallery link.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div 
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    dragActive 
                      ? "border-blue-500 bg-blue-50" 
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <Camera className="h-12 w-12 text-gray-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Upload your event photos</h3>
                      <p className="text-gray-600">Drag and drop photos here, or click to browse your file explorer</p>
                      <p className="text-sm text-gray-500 mt-2">
                        Supports: JPG, PNG, GIF (Max 10MB per photo)
                      </p>
                    </div>
                    <div className="flex justify-center gap-3">
                      <Input
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={(e) => handlePhotoUpload(e.target.files)}
                        className="hidden"
                        id="photo-upload"
                        disabled={uploading}
                      />
                      <Button
                        type="button"
                        className="bg-gradient-to-r from-blue-600 to-cyan-600"
                        disabled={uploading}
                        onClick={openFileExplorer}
                      >
                        {uploading ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <FolderOpen className="h-4 w-4 mr-2" />
                        )}
                        Browse Files
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        disabled={uploading}
                        onClick={openFileExplorer}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Select Photos
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Upload Progress */}
                {uploading && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Uploading photos...</span>
                      <span className="text-sm text-gray-600">{Math.round(uploadProgress)}%</span>
                    </div>
                    <Progress value={uploadProgress} className="h-2" />
                  </div>
                )}

                {/* Uploaded Photos */}
                {uploadedPhotos.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Uploaded Photos ({uploadedPhotos.length})</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {uploadedPhotos.map((photo) => (
                        <div key={photo.id} className="border rounded-lg p-4 space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <ImageIcon className="h-5 w-5 text-blue-600" />
                              <span className="text-sm font-medium truncate">{photo.name}</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removePhoto(photo.id)}
                              className="h-6 w-6 p-0 text-red-600 hover:text-red-800"
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                          </div>

                          {photo.url && (
                            <img
                              src={photo.url}
                              alt={photo.name}
                              className="w-full h-32 object-cover rounded"
                            />
                          )}

                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">
                              {photo.size ? (photo.size / 1024 / 1024).toFixed(1) + " MB" : "Unknown size"}
                            </span>
                            <div className="flex items-center gap-1">
                              <Check className="h-4 w-4 text-green-600" />
                              <Badge variant="outline" className="text-xs">
                                Uploaded
                              </Badge>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor={`caption-${photo.id}`} className="text-xs">
                              Caption (Optional)
                            </Label>
                            <Textarea
                              id={`caption-${photo.id}`}
                              placeholder="Add a caption for this photo..."
                              className="text-xs"
                              rows={2}
                              value={photo.caption}
                              onChange={(e) => {
                                const newCaption = e.target.value
                                setUploadedPhotos((prev) =>
                                  prev.map((p) => (p.id === photo.id ? { ...p, caption: newCaption } : p)),
                                )
                              }}
                              onBlur={(e) => updatePhotoCaption(photo.id, e.target.value)}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Guidelines */}
          <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-emerald-50">
            <CardHeader>
              <CardTitle className="text-green-900">Photo Upload Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-start gap-2">
                <Check className="h-4 w-4 mt-0.5 text-green-600" />
                <span className="text-sm">Upload high-quality photos (minimum 1920x1080 for best results)</span>
              </div>
              <div className="flex items-start gap-2">
                <Check className="h-4 w-4 mt-0.5 text-green-600" />
                <span className="text-sm">Add descriptive captions to help guests find their photos</span>
              </div>
              <div className="flex items-start gap-2">
                <Check className="h-4 w-4 mt-0.5 text-green-600" />
                <span className="text-sm">Organize photos by event moments (ceremony, reception, etc.)</span>
              </div>
              <div className="flex items-start gap-2">
                <Check className="h-4 w-4 mt-0.5 text-green-600" />
                <span className="text-sm">The gallery link will be automatically included in thank you messages</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 