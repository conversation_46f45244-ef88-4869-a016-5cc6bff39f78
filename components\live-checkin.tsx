"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Users, Clock, CheckCircle, Loader2, QrCode } from "lucide-react"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export function LiveCheckin() {
  const [events, setEvents] = useState<any[]>([])
  const [selectedEventId, setSelectedEventId] = useState("")
  const [checkinData, setCheckinData] = useState({
    totalGuests: 0,
    checkedIn: 0,
    pending: 0,
    lateArrivals: 0,
  })
  const [recentCheckins, setRecentCheckins] = useState<any[]>([])
  const [qrCode, setQrCode] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    loadEvents()
  }, [])

  useEffect(() => {
    if (selectedEventId) {
      loadCheckinData()
      loadRecentCheckins()
      // Set up real-time updates
      const interval = setInterval(() => {
        loadCheckinData()
        loadRecentCheckins()
      }, 30000) // Update every 30 seconds

      return () => clearInterval(interval)
    }
  }, [selectedEventId])

  const loadEvents = async () => {
    try {
      const eventsData = await apiClient.getEvents()
      setEvents(eventsData)
      if (eventsData.length > 0) {
        setSelectedEventId(eventsData[0].id.toString())
      }
    } catch (error) {
      console.error("Failed to load events:", error)
      toast.error("Failed to load events")
    }
  }

  const loadCheckinData = async () => {
    if (!selectedEventId) return

    try {
      setIsLoading(true)
      const guests = await apiClient.getGuests(Number.parseInt(selectedEventId))

      const totalGuests = guests.length
      const checkedIn = guests.filter((g: { checked_in: any }) => g.checked_in).length
      const pending = totalGuests - checkedIn
      const lateArrivals = guests.filter((g: { checked_in: any; late_arrival: any }) => g.checked_in && g.late_arrival).length

      setCheckinData({
        totalGuests,
        checkedIn,
        pending,
        lateArrivals,
      })
    } catch (error) {
      console.error("Failed to load checkin data:", error)
      toast.error("Failed to load checkin data")
    } finally {
      setIsLoading(false)
    }
  }

  const loadRecentCheckins = async () => {
    if (!selectedEventId) return

    try {
      const guests = await apiClient.getGuests(Number.parseInt(selectedEventId))
      const checkedInGuests = guests
        .filter((g: { checked_in: any }) => g.checked_in)
        .sort((a: { checkin_time: string | number | Date | null }, b: { checkin_time: string | number | Date | null }) => {
          const dateA = a.checkin_time ? new Date(a.checkin_time).getTime() : 0
          const dateB = b.checkin_time ? new Date(b.checkin_time).getTime() : 0
          return dateB - dateA
        })
        .slice(0, 10)
        .map((guest: { id: any; first_name: any; last_name: any; checkin_time: string | number | Date | null; table_number: any }) => ({
          id: guest.id,
          name: `${guest.first_name} ${guest.last_name}`,
          time: guest.checkin_time ? new Date(guest.checkin_time).toLocaleTimeString() : "Just now",
          table: guest.table_number ? `Table ${guest.table_number}` : "No table assigned",
          status: "checked-in",
        }))

      setRecentCheckins(checkedInGuests)
    } catch (error) {
      console.error("Failed to load recent checkins:", error)
    }
  }

  const handleQrScan = async () => {
    if (!qrCode.trim()) {
      toast.error("Please enter a QR code")
      return
    }

    setIsProcessing(true)
    try {
      // Extract guest ID from QR code (assuming format: "guest_123" or just "123")
      const guestId = qrCode.replace(/\D/g, "")

      if (!guestId) {
        toast.error("Invalid QR code format")
        return
      }

      // Check in guest to present guests list
      const result = await apiClient.checkInGuestToPresentList(
        Number.parseInt(selectedEventId),
        Number.parseInt(guestId),
        "qr_scan",
        "QR code scan"
      )

      if (result.message) {
        toast.success(`${result.guest.name} checked in successfully and added to present guests list!`)
        setQrCode("")
        loadCheckinData()
        loadRecentCheckins()
      } else {
        toast.error(result.error || "Failed to check in guest")
      }
    } catch (error) {
      console.error("QR scan error:", error)
      toast.error("Failed to process QR code")
    } finally {
      setIsProcessing(false)
    }
  }

  const handleManualCheckin = async (guestId: any) => {
    try {
      const result = await apiClient.checkInGuestToPresentList(
        Number.parseInt(selectedEventId),
        guestId,
        "manual_search",
        "Manual check-in"
      )

      if (result.message) {
        toast.success("Guest checked in successfully and added to present guests list!")
        loadCheckinData()
        loadRecentCheckins()
      } else {
        toast.error(result.error || "Failed to check in guest")
      }
    } catch (error) {
      toast.error("Failed to check in guest")
    }
  }

  if (isLoading && !selectedEventId) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading events...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Event Selection */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Select value={selectedEventId} onValueChange={setSelectedEventId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select an event for check-in" />
                </SelectTrigger>
                <SelectContent>
                  {events.map((event) => (
                    <SelectItem key={event.id} value={event.id.toString()}>
                      {event.title} - {new Date(event.event_date).toLocaleDateString()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* QR Code Scanner */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">QR Code Check-in</h3>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <Input
                placeholder="Scan or enter QR code..."
                value={qrCode}
                onChange={(e) => setQrCode(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleQrScan()}
              />
            </div>
            <Button
              onClick={handleQrScan}
              disabled={isProcessing || !qrCode.trim()}
              className="bg-gradient-to-r from-green-600 to-emerald-600"
            >
              {isProcessing ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <QrCode className="h-4 w-4 mr-2" />}
              Check In
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Check-in Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Guests</p>
                <p className="text-3xl font-bold text-gray-900 mt-2">{checkinData.totalGuests}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Checked In</p>
                <p className="text-3xl font-bold text-green-600 mt-2">{checkinData.checkedIn}</p>
                <p className="text-sm text-green-600 mt-1">
                  {checkinData.totalGuests > 0
                    ? Math.round((checkinData.checkedIn / checkinData.totalGuests) * 100)
                    : 0}
                  %
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-3xl font-bold text-amber-600 mt-2">{checkinData.pending}</p>
              </div>
              <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-red-50 to-white">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Late Arrivals</p>
                <p className="text-3xl font-bold text-red-600 mt-2">{checkinData.lateArrivals}</p>
              </div>
              <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Check-ins */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Check-ins</h3>
          <div className="space-y-3">
            {recentCheckins.map((checkin) => (
              <div key={checkin.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium">{checkin.name}</p>
                    <p className="text-sm text-gray-600">{checkin.table}</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge className="bg-green-100 text-green-800">Checked In</Badge>
                  <p className="text-xs text-gray-500 mt-1">{checkin.time}</p>
                </div>
              </div>
            ))}

            {recentCheckins.length === 0 && (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-gray-600">No recent check-ins</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
