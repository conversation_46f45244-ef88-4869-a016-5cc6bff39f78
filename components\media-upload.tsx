"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Upload, ImageIcon, Video, X, Check, Eye, Loader2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export function MediaUpload() {
  const [events, setEvents] = useState([])
  const [selectedEvent, setSelectedEvent] = useState("")
  const [uploadedFiles, setUploadedFiles] = useState([])
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadEvents()
  }, [])

  useEffect(() => {
    if (selectedEvent) {
      loadExistingMedia()
    }
  }, [selectedEvent])

  const loadEvents = async () => {
    try {
      const eventsData = await apiClient.getEvents()
      setEvents(eventsData)
      if (eventsData.length > 0) {
        setSelectedEvent(eventsData[0].id.toString())
      }
    } catch (error) {
      console.error("Failed to load events:", error)
      toast.error("Failed to load events")
    } finally {
      setIsLoading(false)
    }
  }

  const loadExistingMedia = async () => {
    if (!selectedEvent) return

    try {
      const media = await apiClient.getEventMedia(Number.parseInt(selectedEvent))
      const formattedMedia = media.map((item) => ({
        id: item.id,
        name: item.file_name,
        size: item.file_size || 0,
        type: item.file_type?.startsWith("image/") ? "image" : "video",
        url: item.file_url,
        uploaded: true,
        caption: item.description || "",
      }))
      setUploadedFiles(formattedMedia)
    } catch (error) {
      console.error("Failed to load existing media:", error)
    }
  }

  const handleFileUpload = async (files) => {
    if (!files || !selectedEvent) return

    setUploading(true)
    setUploadProgress(0)

    try {
      const fileArray = Array.from(files)
      const totalFiles = fileArray.length
      let completedFiles = 0

      const uploadPromises = fileArray.map(async (file) => {
        try {
          const result = await apiClient.uploadFile(file, Number.parseInt(selectedEvent), `Media upload - ${file.name}`)

          const newFile = {
            id: result.id,
            name: file.name,
            size: file.size,
            type: file.type.startsWith("image/") ? "image" : "video",
            url: result.fileUrl,
            uploaded: true,
            caption: "",
          }

          completedFiles++
          setUploadProgress((completedFiles / totalFiles) * 100)

          return newFile
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error)
          toast.error(`Failed to upload ${file.name}`)
          return null
        }
      })

      const results = await Promise.all(uploadPromises)
      const successfulUploads = results.filter((result) => result !== null)

      setUploadedFiles((prev) => [...prev, ...successfulUploads])

      if (successfulUploads.length > 0) {
        toast.success(`Successfully uploaded ${successfulUploads.length} file(s)`)
      }
    } catch (error) {
      console.error("Upload error:", error)
      toast.error("Failed to upload files")
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }

  const removeFile = async (id) => {
    try {
      await apiClient.deleteFile(id)
      setUploadedFiles((prev) => prev.filter((file) => file.id !== id))
      toast.success("File removed successfully")
    } catch (error) {
      console.error("Failed to remove file:", error)
      toast.error("Failed to remove file")
    }
  }

  const updateFileCaption = async (fileId, caption) => {
    try {
      await apiClient.updateFile(fileId, { description: caption })
      setUploadedFiles((prev) => prev.map((file) => (file.id === fileId ? { ...file, caption } : file)))
      toast.success("Caption updated successfully")
    } catch (error) {
      console.error("Failed to update caption:", error)
      toast.error("Failed to update caption")
    }
  }

  const handlePublishGallery = async () => {
    if (!selectedEvent || uploadedFiles.length === 0) {
      toast.error("No files to publish")
      return
    }

    try {
      await apiClient.publishEventGallery(Number.parseInt(selectedEvent))
      toast.success("Gallery published successfully! Guests will be notified.")
    } catch (error) {
      console.error("Failed to publish gallery:", error)
      toast.error("Failed to publish gallery")
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading events...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle>Upload Event Media</CardTitle>
          <CardDescription>Upload photos and videos from your assigned events</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Event Selection */}
          <div className="space-y-2">
            <Label htmlFor="event">Select Event</Label>
            <Select value={selectedEvent} onValueChange={setSelectedEvent}>
              <SelectTrigger>
                <SelectValue placeholder="Choose an event to upload media for" />
              </SelectTrigger>
              <SelectContent>
                {events.map((event) => (
                  <SelectItem key={event.id} value={event.id.toString()}>
                    {event.title} - {new Date(event.event_date).toLocaleDateString()}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Upload Area */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
            <div className="space-y-4">
              <div className="flex justify-center">
                <Upload className="h-12 w-12 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Upload your media files</h3>
                <p className="text-gray-600">Drag and drop files here, or click to browse</p>
                <p className="text-sm text-gray-500 mt-2">Supports: JPG, PNG, MP4, MOV (Max 100MB per file)</p>
              </div>
              <div className="flex justify-center">
                <Input
                  type="file"
                  multiple
                  accept="image/*,video/*"
                  onChange={(e) => handleFileUpload(e.target.files)}
                  className="hidden"
                  id="file-upload"
                  disabled={!selectedEvent || uploading}
                />
                <Label htmlFor="file-upload" className="cursor-pointer">
                  <Button
                    type="button"
                    className="bg-gradient-to-r from-green-600 to-emerald-600"
                    disabled={!selectedEvent || uploading}
                  >
                    {uploading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4 mr-2" />
                    )}
                    Choose Files
                  </Button>
                </Label>
              </div>
            </div>
          </div>

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Uploading files...</span>
                <span className="text-sm text-gray-600">{Math.round(uploadProgress)}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          {/* Uploaded Files */}
          {uploadedFiles.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Uploaded File ({uploadedFiles.length})</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {uploadedFiles.map((file) => (
                  <div key={file.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {file.type === "image" ? (
                          <ImageIcon className="h-5 w-5 text-blue-600" />
                        ) : (
                          <Video className="h-5 w-5 text-purple-600" />
                        )}
                        <span className="text-sm font-medium truncate">{file.name}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        className="h-6 w-6 p-0 text-red-600 hover:text-red-800"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    {file.type === "image" && (
                      <img
                        src={file.url || "/placeholder.svg"}
                        alt={file.name}
                        className="w-full h-32 object-cover rounded"
                      />
                    )}

                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {file.size ? (file.size / 1024 / 1024).toFixed(1) + " MB" : "Unknown size"}
                      </span>
                      <div className="flex items-center gap-1">
                        <Check className="h-4 w-4 text-green-600" />
                        <Badge variant="outline" className="text-xs">
                          Uploaded
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`caption-${file.id}`} className="text-xs">
                        Caption (Optional)
                      </Label>
                      <Textarea
                        id={`caption-${file.id}`}
                        placeholder="Add a caption for this media..."
                        className="text-xs"
                        rows={2}
                        value={file.caption}
                        onChange={(e) => {
                          const newCaption = e.target.value
                          setUploadedFiles((prev) =>
                            prev.map((f) => (f.id === file.id ? { ...f, caption: newCaption } : f)),
                          )
                        }}
                        onBlur={(e) => updateFileCaption(file.id, e.target.value)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          {uploadedFiles.length > 0 && (
            <div className="flex items-center justify-between pt-4 border-t">
              <div className="text-sm text-gray-600">{uploadedFiles.length} files ready to publish</div>
              <div className="flex items-center gap-2">
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview Gallery
                </Button>
                <Button className="bg-gradient-to-r from-green-600 to-emerald-600" onClick={handlePublishGallery}>
                  Publish to Event Gallery
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Guidelines */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="text-green-900">Upload Guidelines</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-green-800">
          <div className="flex items-start gap-2">
            <Check className="h-4 w-4 mt-0.5 text-green-600" />
            <span className="text-sm">Upload high-quality images (minimum 1920x1080 for photos)</span>
          </div>
          <div className="flex items-start gap-2">
            <Check className="h-4 w-4 mt-0.5 text-green-600" />
            <span className="text-sm">Add descriptive captions to help guests find their photos</span>
          </div>
          <div className="flex items-start gap-2">
            <Check className="h-4 w-4 mt-0.5 text-green-600" />
            <span className="text-sm">Organize photos by event moments (ceremony, reception, etc.)</span>
          </div>
          <div className="flex items-start gap-2">
            <Check className="h-4 w-4 mt-0.5 text-green-600" />
            <span className="text-sm">Guests will receive automatic notifications when photos are published</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
