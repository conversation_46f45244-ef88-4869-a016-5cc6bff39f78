"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { AlertTriangle, ChevronDown, ChevronRight, Users, Database, FileText, AlertCircle } from "lucide-react"
import { CSVDuplicate } from "@/lib/services/csv-upload.service"

interface DuplicateNotificationProps {
  duplicates: CSVDuplicate[]
  onResolve?: (duplicate: CSVDuplicate, action: 'skip' | 'override') => void
  showActions?: boolean
}

export function DuplicateNotification({ 
  duplicates, 
  onResolve, 
  showActions = false 
}: DuplicateNotificationProps) {
  const [isOpen, setIsOpen] = useState(true)
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({})

  if (duplicates.length === 0) return null

  // Group duplicates by type
  const groupedDuplicates = duplicates.reduce((groups, duplicate) => {
    const type = duplicate.duplicate_type
    if (!groups[type]) {
      groups[type] = []
    }
    groups[type].push(duplicate)
    return groups
  }, {} as Record<string, CSVDuplicate[]>)

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'database':
        return <Database className="h-4 w-4" />
      case 'file':
        return <FileText className="h-4 w-4" />
      case 'error':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'database':
        return 'Already in Database'
      case 'file':
        return 'Duplicates in File'
      case 'error':
        return 'Import Errors'
      default:
        return 'Other Issues'
    }
  }

  const getTypeDescription = (type: string) => {
    switch (type) {
      case 'database':
        return 'These guests already exist in your event database'
      case 'file':
        return 'These guests appear multiple times in the uploaded file'
      case 'error':
        return 'These guests could not be imported due to errors'
      default:
        return 'Other issues encountered during import'
    }
  }

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'database':
        return 'bg-blue-100 text-blue-800'
      case 'file':
        return 'bg-amber-100 text-amber-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const toggleGroup = (type: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }

  return (
    <Card className="border-amber-200 bg-amber-50">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-600" />
            <CardTitle className="text-amber-800">
              {duplicates.length} Duplicate{duplicates.length > 1 ? 's' : ''} Found
            </CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </Button>
        </div>
        <CardDescription className="text-amber-700">
          Some guests were not imported due to duplicates or errors. Review the details below.
        </CardDescription>
      </CardHeader>

      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleContent>
          <CardContent className="space-y-4">
            {Object.entries(groupedDuplicates).map(([type, typeDuplicates]) => (
              <div key={type} className="space-y-2">
                <div 
                  className="flex items-center justify-between p-3 bg-white rounded-lg border cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleGroup(type)}
                >
                  <div className="flex items-center gap-3">
                    {getTypeIcon(type)}
                    <div>
                      <h4 className="font-medium">{getTypeLabel(type)}</h4>
                      <p className="text-sm text-gray-600">{getTypeDescription(type)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getTypeBadgeColor(type)}>
                      {typeDuplicates.length}
                    </Badge>
                    {expandedGroups[type] ? 
                      <ChevronDown className="h-4 w-4" /> : 
                      <ChevronRight className="h-4 w-4" />
                    }
                  </div>
                </div>

                <Collapsible open={expandedGroups[type]} onOpenChange={() => toggleGroup(type)}>
                  <CollapsibleContent>
                    <div className="ml-6 space-y-2">
                      {typeDuplicates.map((duplicate, index) => (
                        <div key={index} className="p-3 bg-white rounded border-l-4 border-l-amber-400">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="font-medium">{duplicate.name}</div>
                              <div className="text-sm text-gray-600 space-y-1">
                                {duplicate.email && (
                                  <div>Email: {duplicate.email}</div>
                                )}
                                {duplicate.phone && (
                                  <div>Phone: {duplicate.phone}</div>
                                )}
                                {duplicate.table_number && (
                                  <div>Table: {duplicate.table_number}</div>
                                )}
                                <div className="text-amber-700 font-medium">
                                  Reason: {duplicate.reason}
                                </div>
                                {duplicate.rowNumber && (
                                  <div className="text-xs text-gray-500">
                                    Row {duplicate.rowNumber} in CSV file
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            {showActions && onResolve && (
                              <div className="flex gap-2 ml-4">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => onResolve(duplicate, 'skip')}
                                >
                                  Skip
                                </Button>
                                {type === 'database' && (
                                  <Button
                                    size="sm"
                                    variant="destructive"
                                    onClick={() => onResolve(duplicate, 'override')}
                                  >
                                    Override
                                  </Button>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            ))}

            <Alert>
              <Users className="h-4 w-4" />
              <AlertTitle>What happens next?</AlertTitle>
              <AlertDescription>
                Duplicates are automatically skipped to prevent data conflicts. 
                You can manually add these guests later or update existing guest information 
                using the edit feature.
              </AlertDescription>
            </Alert>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}

export default DuplicateNotification
