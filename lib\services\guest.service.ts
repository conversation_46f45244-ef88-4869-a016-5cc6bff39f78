import apiClient from "@/lib/api"

export interface Guest {
  id: number
  event_id: number
  name: string
  email: string
  phone: string
  table_number: string
  seat_number: string
  dietary_requirements: string
  plus_one: boolean
  rsvp_status: "pending" | "confirmed" | "declined"
  checked_in: boolean
  check_in_time: string | null
  whatsapp_sent: boolean
  whatsapp_sent_at: string | null
  qr_code: string | null
  created_at: string
  updated_at: string
}

export interface CreateGuestData {
  name: string
  email: string
  phone: string
  table_number?: string
  seat_number?: string
  dietary_requirements?: string
  plus_one?: boolean
}

class GuestService {
  async getGuests(eventId: number): Promise<Guest[]> {
    return apiClient.request(`/events/${eventId}/guests`)
  }

  async createGuest(eventId: number, guestData: CreateGuestData): Promise<Guest> {
    return apiClient.request(`/events/${eventId}/guests`, {
      method: "POST",
      body: guestData,
    })
  }

  async updateGuest(eventId: number, guestId: number, guestData: Partial<Guest>): Promise<Guest> {
    return apiClient.request(`/events/${eventId}/guests/${guestId}`, {
      method: "PUT",
      body: guestData,
    })
  }

  async deleteGuest(eventId: number, guestId: number): Promise<void> {
    return apiClient.request(`/events/${eventId}/guests/${guestId}`, {
      method: "DELETE",
    })
  }

  async generateQRCode(eventId: number, guestId: number): Promise<string> {
    const response = await apiClient.request(`/events/${eventId}/guests/${guestId}/qr`)
    return response.qrCode
  }

  async checkInGuest(qrData: string): Promise<any> {
    return apiClient.request("/checkin", {
      method: "POST",
      body: { qrData },
    })
  }
}

export const guestService = new GuestService()
