"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Calendar, Users, MapPin, MoreHorizontal, Loader2 } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export function EventsTable() {
  const [events, setEvents] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadEvents()
  }, [])

  const loadEvents = async () => {
    try {
      setIsLoading(true)
      const eventsData = await apiClient.getEvents()

      // Transform the data to include additional info
      const eventsWithDetails = await Promise.all(
        eventsData.map(async (event) => {
          try {
            const guests = await apiClient.getGuests(event.id)
            const planner = await apiClient.getUser(event.planner_id)

            return {
              id: event.id,
              name: event.title,
              date: new Date(event.event_date).toLocaleDateString(),
              venue: event.venue,
              guests: guests.length,
              status: event.status,
              planner: planner ? `${planner.first_name} ${planner.last_name}` : "Unassigned",
            }
          } catch (error) {
            return {
              id: event.id,
              name: event.title,
              date: new Date(event.event_date).toLocaleDateString(),
              venue: event.venue,
              guests: 0,
              status: event.status,
              planner: "Unassigned",
            }
          }
        }),
      )

      setEvents(eventsWithDetails)
    } catch (error) {
      console.error("Failed to load events:", error)
      toast.error("Failed to load events")
    } finally {
      setIsLoading(false)
    }
  }

  const handleViewDetails = (eventId) => {
    // Navigate to event details page
    window.location.href = `/admin/events/${eventId}`
  }

  const handleEditEvent = (eventId) => {
    // Open edit dialog or navigate to edit page
    console.log("Edit event:", eventId)
    toast.info("Edit functionality coming soon")
  }

  const handleManageGuests = (eventId) => {
    // Navigate to guest management
    window.location.href = `/planner/guests?event=${eventId}`
  }

  const handleCancelEvent = async (eventId) => {
    if (!confirm("Are you sure you want to cancel this event?")) return

    try {
      await apiClient.updateEvent(eventId, { status: "cancelled" })
      toast.success("Event cancelled successfully")
      loadEvents() // Reload the table
    } catch (error) {
      toast.error("Failed to cancel event")
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case "planning":
        return <Badge variant="secondary">Planning</Badge>
      case "completed":
        return <Badge variant="outline">Completed</Badge>
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 border rounded-lg">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading events...</span>
      </div>
    )
  }

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Event</TableHead>
            <TableHead>Date & Venue</TableHead>
            <TableHead>Guests</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Planner</TableHead>
            <TableHead className="w-[50px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {events.map((event) => (
            <TableRow key={event.id}>
              <TableCell>
                <div className="font-medium">{event.name}</div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  <div className="flex items-center gap-1 text-sm">
                    <Calendar className="h-3 w-3" />
                    {event.date}
                  </div>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <MapPin className="h-3 w-3" />
                    {event.venue}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4 text-gray-600" />
                  {event.guests}
                </div>
              </TableCell>
              <TableCell>{getStatusBadge(event.status)}</TableCell>
              <TableCell>
                <span className="text-sm">{event.planner}</span>
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewDetails(event.id)}>View Details</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleEditEvent(event.id)}>Edit Event</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleManageGuests(event.id)}>Manage Guests</DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600" onClick={() => handleCancelEvent(event.id)}>
                      Cancel Event
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {events.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
          <p className="text-gray-600">Create your first event to get started</p>
        </div>
      )}
    </div>
  )
}
