"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Search, Filter, Heart, Star, Sparkles } from "lucide-react"
import Link from "next/link"

const templates = [
	{
		id: 5,
		name: "Birthday Bash",
		category: "birthday",
		style: "fun",
		popular: true,
		preview: {
			background: "url('/images/hbd/1.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 1,
		name: "Elegant Wedding",
		category: "wedding",
		style: "elegant",
		popular: true,
		preview: {
			background: "url('/images/wedding/1.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 9,
		name: "Conference 2024",
		category: "corporate",
		style: "professional",
		popular: true,
		preview: {
			background: "url('/images/conference/1.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 14,
		name: "Holiday Cheer",
		category: "holiday",
		style: "festive",
		popular: true,
		preview: {
			background: "url('/images/holiday/1.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 7,
		name: "Baby Shower Joy",
		category: "baby",
		style: "cute",
		popular: true,
		preview: {
			background: "url('/images/babyShower/1.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 2,
		name: "Classic Wedding",
		category: "wedding",
		style: "classic",
		popular: false,
		preview: {
			background: "url('/images/wedding/2.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 6,
		name: "Birthday Surprise",
		category: "birthday",
		style: "colorful",
		popular: false,
		preview: {
			background: "url('/images/hbd/2.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 10,
		name: "Tech Meetup",
		category: "corporate",
		style: "modern",
		popular: false,
		preview: {
			background: "url('/images/conference/2.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 3,
		name: "Modern Wedding",
		category: "wedding",
		style: "modern",
		popular: false,
		preview: {
			background: "url('/images/wedding/3.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 8,
		name: "Little One's Shower",
		category: "baby",
		style: "playful",
		popular: false,
		preview: {
			background: "url('/images/babyShower/2.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 4,
		name: "Romantic Wedding",
		category: "wedding",
		style: "romantic",
		popular: false,
		preview: {
			background: "url('/images/wedding/4.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 11,
		name: "Graduation Gala",
		category: "graduation",
		style: "modern",
		popular: false,
		preview: {
			background: "url('/images/graduation/1.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 12,
		name: "Cap & Gown Party",
		category: "graduation",
		style: "classic",
		popular: false,
		preview: {
			background: "url('/images/graduation/2.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 13,
		name: "Graduation Celebration",
		category: "graduation",
		style: "festive",
		popular: false,
		preview: {
			background: "url('/images/graduation/3.png') center/cover no-repeat",
			elements: [],
		},
	},
	{
		id: 15,
		name: "Festive Gathering",
		category: "holiday",
		style: "classic",
		popular: false,
		preview: {
			background: "url('/images/holiday/2.png') center/cover no-repeat",
			elements: [],
		},
	},
]

const categories = [
	{ id: "all", name: "All Templates", count: templates.length },
	{ id: "wedding", name: "Wedding", count: templates.filter((t) => t.category === "wedding").length },
	{ id: "birthday", name: "Birthday", count: templates.filter((t) => t.category === "birthday").length },
	{ id: "corporate", name: "Corporate", count: templates.filter((t) => t.category === "corporate").length },
	{ id: "holiday", name: "Holiday", count: templates.filter((t) => t.category === "holiday").length },
	{ id: "baby", name: "Baby Shower", count: templates.filter((t) => t.category === "baby").length },
	{ id: "graduation", name: "Graduation", count: templates.filter((t) => t.category === "graduation").length },
]

export default function HomePage() {
	const [searchTerm, setSearchTerm] = useState("")
	const [selectedCategory, setSelectedCategory] = useState("all")
	const [showFilters, setShowFilters] = useState(false)

	const filteredTemplates = templates.filter((template) => {
		const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase())
		const matchesCategory = selectedCategory === "all" || template.category === selectedCategory
		return matchesSearch && matchesCategory
	})

	return (
		<div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
			{/* Header */}
			<header className="bg-white/80 backdrop-blur-sm shadow-sm border-b sticky top-0 z-50">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center h-16">
						<div className="flex items-center space-x-2">
							<Sparkles className="w-8 h-8 text-purple-600" />
							<h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
								InviteDesigner
							</h1>
						</div>
						<div className="flex items-center space-x-4">
							<Button variant="outline" className="hidden sm:flex bg-transparent">
								Templates
							</Button>
							<Link href="/editor">
								<Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
									Create New
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</header>

			{/* Hero Section */}
			<section className="py-16 px-4 text-center">
				<div className="max-w-4xl mx-auto">
					<h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
						Create Stunning
						<span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
							{" "}
							Invitations
						</span>
					</h2>
					<p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
						Design beautiful, professional invitations in minutes with our intuitive drag-and-drop editor. Perfect for
						weddings, birthdays, corporate events, and more.
					</p>

					{/* Search Bar */}
					<div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-lg mx-auto mb-8">
						<div className="relative flex-1 w-full">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
							<Input
								placeholder="Search templates..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="pl-10 h-12 border-2 border-purple-200 focus:border-purple-400"
							/>
						</div>
						<Button
							variant="outline"
							onClick={() => setShowFilters(!showFilters)}
							className="h-12 px-6 border-2 border-purple-200"
						>
							<Filter className="w-4 h-4 mr-2" />
							Filters
						</Button>
					</div>

					{/* Quick Start */}
					<div className="flex flex-wrap justify-center gap-4">
						<Link href="/editor">
							<Button
								size="lg"
								className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
							>
								Start from Blank
							</Button>
						</Link>
						<Button size="lg" variant="outline" onClick={() => setSelectedCategory("wedding")}>
							Wedding Templates
						</Button>
						<Button size="lg" variant="outline" onClick={() => setSelectedCategory("birthday")}>
							Birthday Templates
						</Button>
					</div>
				</div>
			</section>

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
				<div className="flex flex-col lg:flex-row gap-8">
					{/* Sidebar Filters */}
					<aside className={`lg:w-64 ${showFilters ? "block" : "hidden lg:block"}`}>
						<div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6 sticky top-24">
							<h3 className="font-semibold text-lg mb-4 text-gray-800">Categories</h3>
							<div className="space-y-2">
								{categories.map((category) => (
									<button
										key={category.id}
										onClick={() => setSelectedCategory(category.id)}
										className={`w-full text-left px-4 py-3 rounded-lg transition-all duration-200 ${
											selectedCategory === category.id
												? "bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 shadow-md"
												: "hover:bg-gray-100"
										}`}
									>
										<div className="flex justify-between items-center">
											<span className="font-medium">{category.name}</span>
											<Badge variant="secondary" className="text-xs bg-gray-200">
												{category.count}
											</Badge>
										</div>
									</button>
								))}
							</div>
						</div>
					</aside>

					{/* Templates Grid */}
					<main className="flex-1">
						<div className="flex justify-between items-center mb-8">
							<div>
								<h2 className="text-3xl font-bold text-gray-900">
									{selectedCategory === "all"
										? "All Templates"
										: categories.find((c) => c.id === selectedCategory)?.name}
								</h2>
								<p className="text-gray-600 mt-1">{filteredTemplates.length} templates available</p>
							</div>
							<div className="flex items-center gap-2">
								<Button variant="outline" size="sm" className="hidden sm:flex bg-transparent">
									<Star className="w-4 h-4 mr-1" />
									Popular
								</Button>
								<Button variant="outline" size="sm" className="hidden sm:flex bg-transparent">
									Newest
								</Button>
							</div>
						</div>

						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
							{filteredTemplates.map((template) => (
								<Card
									key={template.id}
									className="group hover:shadow-xl transition-all duration-300 cursor-pointer border-0 bg-white/80 backdrop-blur-sm"
								>
									<CardContent className="p-0">
										<div className="relative overflow-hidden rounded-t-lg">
											<div
												className="w-full h-64 flex flex-col items-center justify-center p-6 relative"
												style={{ background: template.preview.background }}
											>
												<div className="text-center space-y-3">
													{/* Removed template.preview.elements text */}
												</div>

												{/* Add a subtle overlay to make it look more like a card */}
												<div className="absolute inset-0 bg-white bg-opacity-10 rounded-t-lg"></div>
											</div>

											<div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-t-lg flex items-center justify-center">
												<Link href={`/editor?template=${template.id}`}>
													<Button className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-95 group-hover:scale-100 bg-white text-gray-900 hover:bg-gray-100">
														Use Template
													</Button>
												</Link>
											</div>

											{template.popular && (
												<Badge className="absolute top-3 left-3 bg-gradient-to-r from-orange-500 to-red-500 text-white border-0">
													<Star className="w-3 h-3 mr-1" />
													Popular
												</Badge>
											)}

											<Button
												variant="ghost"
												size="sm"
												className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity bg-white/80 hover:bg-white"
											>
												<Heart className="w-4 h-4" />
											</Button>
										</div>

										<div className="p-4">
											<h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
											<div className="flex items-center gap-2">
												<Badge variant="outline" className="text-xs capitalize border-purple-200 text-purple-700">
													{template.category}
												</Badge>
												<Badge variant="outline" className="text-xs capitalize border-pink-200 text-pink-700">
													{template.style}
												</Badge>
											</div>
										</div>
									</CardContent>
								</Card>
							))}

							{filteredTemplates.length === 0 && (
								<div className="text-center py-16">
									<div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
										<Search className="w-8 h-8 text-gray-400" />
									</div>
									<h3 className="text-xl font-semibold text-gray-900 mb-2">No templates found</h3>
									<p className="text-gray-600 mb-6">Try adjusting your search or filter criteria</p>
									<Button
										onClick={() => {
											setSearchTerm("")
											setSelectedCategory("all")
										}}
										variant="outline"
										className="border-2 border-purple-200"
									>
										Clear Filters
									</Button>
								</div>
							)}
						</div>
					</main>
				</div>
			</div>
		</div>
	)
}
