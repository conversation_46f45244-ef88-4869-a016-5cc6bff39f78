"use client"

import React, { useRef, useState } from "react"
import { Stage, Layer, Text, Rect, Image as KonvaImage, Group } from "react-konva"
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SketchPicker } from "react-color"
import htmlToImage from "html-to-image"
import jsPDF from "jspdf"
import { MoveUp, MoveDown, Copy, Trash2, RotateCcw, RotateCw, ArrowUp, ArrowDown } from "lucide-react"

// Helper to load image from file
function useImageUpload() {
  const [img, setImg] = useState<HTMLImageElement | null>(null)
  const upload = (file: File) => {
    const reader = new FileReader()
    reader.onload = () => {
      const image = new window.Image()
      image.src = reader.result as string
      image.onload = () => setImg(image)
    }
    reader.readAsDataURL(file)
  }
  return [img, upload] as const
}

const FONTS = ["Arial", "Georgia", "Times New Roman", "Comic Sans MS", "Impact", "Courier New"]

export default function InvitationDesigner({ initialTemplate, onSave, onCancel }) {
  const stageRef = useRef<any>(null)
  const [elements, setElements] = useState<any[]>([])
  const [selectedId, setSelectedId] = useState<string | null>(null)
  const [background, setBackground] = useState(initialTemplate?.colors?.primary || "#fff")
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [img, uploadImg] = useImageUpload()
  const [backgroundImage, setBackgroundImage] = useState<string | null>(null)

  // Add element
  const addText = () => {
    setElements([
      ...elements,
      {
        id: `text-${Date.now()}`,
        type: "text",
        text: "Edit me!",
        x: 100,
        y: 100,
        fontSize: 24,
        fontFamily: FONTS[0],
        fill: "#222",
        fontStyle: "normal",
        align: "left",
        width: 200,
        draggable: true,
      },
    ])
  }
  const addRect = () => {
    setElements([
      ...elements,
      {
        id: `rect-${Date.now()}`,
        type: "rect",
        x: 120,
        y: 120,
        width: 120,
        height: 60,
        fill: "#eee",
        draggable: true,
      },
    ])
  }
  const addImage = (file: File) => {
    const reader = new FileReader()
    reader.onload = () => {
      setElements([
        ...elements,
        {
          id: `img-${Date.now()}`,
          type: "image",
          src: reader.result,
          x: 80,
          y: 80,
          width: 200,
          height: 120,
          opacity: 1,
          draggable: true,
        },
      ])
    }
    reader.readAsDataURL(file)
  }

  // Update element
  const updateElement = (id: string, props: any) => {
    setElements(elements.map(el => (el.id === id ? { ...el, ...props } : el)))
  }

  // Remove element
  const removeElement = (id: string) => {
    setElements(elements.filter(el => el.id !== id))
    setSelectedId(null)
  }

  // Export as PNG
  const handleExportPNG = async () => {
    const uri = stageRef.current.toDataURL({ pixelRatio: 2 })
    onSave({ image: uri, elements, background, backgroundImage })
  }

  // Export as PDF
  const handleExportPDF = async () => {
    const uri = stageRef.current.toDataURL({ pixelRatio: 2 })
    const pdf = new jsPDF({ orientation: "portrait", unit: "px", format: [400, 600] })
    pdf.addImage(uri, "PNG", 0, 0, 400, 600)
    pdf.save("invitation.pdf")
  }

  // Render selected element controls
  const selected = elements.find(el => el.id === selectedId)

  // New: Layer management
  const bringForward = () => {
    if (!selectedId) return
    const idx = elements.findIndex(el => el.id === selectedId)
    if (idx < elements.length - 1) {
      const newElements = [...elements]
      const [el] = newElements.splice(idx, 1)
      newElements.splice(idx + 1, 0, el)
      setElements(newElements)
    }
  }
  const sendBackward = () => {
    if (!selectedId) return
    const idx = elements.findIndex(el => el.id === selectedId)
    if (idx > 0) {
      const newElements = [...elements]
      const [el] = newElements.splice(idx, 1)
      newElements.splice(idx - 1, 0, el)
      setElements(newElements)
    }
  }
  const duplicateElement = () => {
    if (!selectedId) return
    const el = elements.find(el => el.id === selectedId)
    if (el) setElements([...elements, { ...el, id: `${el.id}-copy-${Date.now()}`, x: el.x + 20, y: el.y + 20 }])
  }

  interface Guest {
  whatsapp_sent: boolean;
  rsvp_status: "pending" | "confirmed" | "declined";
  // Add other properties if needed
}
  return (
    <Dialog open onOpenChange={onCancel}>
      <DialogContent className="max-w-6xl p-0 overflow-hidden">
        {/* Topbar */}
        <div className="flex items-center justify-between px-6 py-3 border-b bg-white">
          <div className="font-bold text-lg">Invitation Designer</div>
          <div className="flex gap-2">
            <Button onClick={handleExportPNG} size="sm">Export PNG</Button>
            <Button onClick={handleExportPDF} size="sm">Export PDF</Button>
            <Button variant="outline" onClick={onCancel} size="sm">Close</Button>
          </div>
        </div>
        <div className="flex h-[700px] bg-gray-50">
          {/* Sidebar */}
          <div className="w-64 border-r bg-white p-6 flex flex-col gap-4">
            <div className="font-semibold mb-2">Elements</div>
            <Button onClick={addText} variant="secondary" size="sm">Add Text</Button>
            <Button onClick={addRect} variant="secondary" size="sm">Add Shape</Button>
            <Button as="label" variant="secondary" size="sm">
              Add Image
              <input type="file" accept="image/*" hidden onChange={e => { const file = e.target.files?.[0]; if (file) addImage(file) }} />
            </Button>
            <div className="font-semibold mt-6 mb-2">Background</div>
            <Button as="label" variant="secondary" size="sm">
              Set Background Image
              <input type="file" accept="image/*" hidden onChange={e => { const file = e.target.files?.[0]; if (file) handleBackgroundImageUpload(file) }} />
            </Button>
            {backgroundImage && (
              <Button size="sm" variant="destructive" onClick={removeBackgroundImage}>Remove Background Image</Button>
            )}
            <Button onClick={() => setShowColorPicker(!showColorPicker)} variant="secondary" size="sm">Set Background Color</Button>
            {showColorPicker && (
              <SketchPicker color={background} onChange={color => setBackground(color.hex)} />
            )}
          </div>
          {/* Canvas Area */}
          <div className="flex-1 flex flex-col items-center justify-center relative">
            <div className="bg-white rounded-lg shadow-lg p-4 mt-8">
              <Stage width={400} height={600} ref={stageRef} style={{ background }} onMouseDown={e => { if (e.target === e.target.getStage()) setSelectedId(null) }}>
                <Layer>
                  {backgroundImage && (
                    <KonvaImage image={(() => { const image = new window.Image(); image.src = backgroundImage; return image })()} x={0} y={0} width={400} height={600} listening={false} />
                  )}
                  {elements.map((el, idx) => {
                    if (el.type === "text") {
                      return (
                        <Text key={el.id} {...el} onClick={() => setSelectedId(el.id)} onTap={() => setSelectedId(el.id)} draggable onDragEnd={e => updateElement(el.id, { x: e.target.x(), y: e.target.y() })} onDblClick={() => { const newText = prompt("Edit text", el.text); if (newText !== null) updateElement(el.id, { text: newText }) }} stroke={selectedId === el.id ? "#0070f3" : undefined} strokeWidth={selectedId === el.id ? 1 : 0} />
                      )
                    }
                    if (el.type === "rect") {
                      return (
                        <Rect key={el.id} {...el} onClick={() => setSelectedId(el.id)} onTap={() => setSelectedId(el.id)} draggable onDragEnd={e => updateElement(el.id, { x: e.target.x(), y: e.target.y() })} stroke={selectedId === el.id ? "#0070f3" : undefined} strokeWidth={selectedId === el.id ? 2 : 0} />
                      )
                    }
                    if (el.type === "image") {
                      return (
                        <KonvaImage key={el.id} image={(() => { const image = new window.Image(); image.src = el.src; return image })()} {...el} onClick={() => setSelectedId(el.id)} onTap={() => setSelectedId(el.id)} draggable onDragEnd={e => updateElement(el.id, { x: e.target.x(), y: e.target.y() })} opacity={el.opacity} stroke={selectedId === el.id ? "#0070f3" : undefined} strokeWidth={selectedId === el.id ? 2 : 0} />
                      )
                    }
                    return null
                  })}
                </Layer>
              </Stage>
            </div>
            <div className="text-xs text-gray-400 mt-2">Drag, double-click to edit text, or select elements for more options.</div>
          </div>
          {/* Properties Panel */}
          <div className="w-80 border-l bg-white p-6 flex flex-col gap-4">
            <div className="font-semibold mb-2">Properties</div>
            {selected && (
              <div className="space-y-2">
                <div className="flex gap-2 mb-2">
                  <Button size="icon" variant="ghost" onClick={bringForward} title="Bring Forward"><ArrowUp className="w-4 h-4" /></Button>
                  <Button size="icon" variant="ghost" onClick={sendBackward} title="Send Backward"><ArrowDown className="w-4 h-4" /></Button>
                  <Button size="icon" variant="ghost" onClick={duplicateElement} title="Duplicate"><Copy className="w-4 h-4" /></Button>
                  <Button size="icon" variant="destructive" onClick={() => removeElement(selected.id)} title="Delete"><Trash2 className="w-4 h-4" /></Button>
                </div>
                {selected.type === "text" && (
                  <>
                    <label>Font</label>
                    <select className="w-full border rounded p-1" value={selected.fontFamily} onChange={e => updateElement(selected.id, { fontFamily: e.target.value })}>{FONTS.map(f => (<option key={f} value={f}>{f}</option>))}</select>
                    <label>Size</label>
                    <input className="w-full border rounded p-1" type="number" value={selected.fontSize} min={8} max={100} onChange={e => updateElement(selected.id, { fontSize: Number(e.target.value) })} />
                    <label>Color</label>
                    <input className="w-full border rounded p-1" type="color" value={selected.fill} onChange={e => updateElement(selected.id, { fill: e.target.value })} />
                    <div className="flex gap-2 mt-2">
                      <Button size="sm" onClick={() => updateElement(selected.id, { fontStyle: selected.fontStyle === "bold" ? "normal" : "bold" })}>Bold</Button>
                      <Button size="sm" onClick={() => updateElement(selected.id, { fontStyle: selected.fontStyle === "italic" ? "normal" : "italic" })}>Italic</Button>
                      <Button size="sm" onClick={() => updateElement(selected.id, { textDecoration: selected.textDecoration === "underline" ? "" : "underline" })}>Underline</Button>
                      <Button size="sm" onClick={() => updateElement(selected.id, { textDecoration: selected.textDecoration === "line-through" ? "" : "line-through" })}>Strike</Button>
                    </div>
                    <label>Align</label>
                    <select className="w-full border rounded p-1" value={selected.align} onChange={e => updateElement(selected.id, { align: e.target.value })}><option value="left">Left</option><option value="center">Center</option><option value="right">Right</option></select>
                  </>
                )}
                {selected.type === "rect" && (
                  <>
                    <label>Color</label>
                    <input className="w-full border rounded p-1" type="color" value={selected.fill} onChange={e => updateElement(selected.id, { fill: e.target.value })} />
                    <label>Width</label>
                    <input className="w-full border rounded p-1" type="number" value={selected.width} min={10} max={400} onChange={e => updateElement(selected.id, { width: Number(e.target.value) })} />
                    <label>Height</label>
                    <input className="w-full border rounded p-1" type="number" value={selected.height} min={10} max={600} onChange={e => updateElement(selected.id, { height: Number(e.target.value) })} />
                  </>
                )}
                {selected.type === "image" && (
                  <>
                    <label>Opacity</label>
                    <input className="w-full" type="range" min={0.1} max={1} step={0.05} value={selected.opacity} onChange={e => updateElement(selected.id, { opacity: Number(e.target.value) })} />
                  </>
                )}
              </div>
            )}
            {!selected && <div className="text-gray-400 text-sm">Select an element to edit its properties.</div>}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
