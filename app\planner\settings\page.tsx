"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { User, Bell, Shield, Palette, Calendar, DollarSign } from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export default function PlannerSettings() {
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    guestCheckin: true,
    vendorUpdates: true,
    eventReminders: true,
    payments: true,
    system: true,
    marketing: false,
  })

  const [eventSettings, setEventSettings] = useState({
    defaultGuestLimit: "500",
    autoCheckin: false,
    qrExpiry: "24",
    allowPlusOnes: true,
    requireRsvp: true,
    defaultInvitationTemplate: "traditional",
    autoSendReminders: true,
    reminderDaysBefore: "7",
    autoGenerateQrCodes: true,
    enableDietaryRequirements: true,
    enableSeatingChart: false,
    defaultEventDuration: "4",
    requireGuestPhone: false,
    enableGuestPhotos: true,
  })

  const [financialSettings, setFinancialSettings] = useState({
    defaultProfitMargin: "25.00",
    currency: "XAF",
    taxRate: "19.25",
    serviceFeePercentage: "5.00",
    paymentTerms: "Net 30",
    invoicePrefix: "INV",
    autoCalculateProfit: true,
  })

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    sessionTimeout: "480",
    passwordExpiry: "90",
    loginNotifications: true,
    suspiciousActivityAlerts: true,
    dataExportNotifications: true,
    accountChangesNotifications: true,
    ipWhitelistEnabled: false,
    allowedIpAddresses: "",
    autoLogoutInactive: true,
    requirePasswordChange: false,
    backupCodesGenerated: false,
  })

  const [appearanceSettings, setAppearanceSettings] = useState({
    theme: "light",
    primaryColor: "#3B82F6",
    sidebarCollapsed: false,
    compactMode: false,
    showAnimations: true,
    fontSize: "medium",
    language: "en",
    timezone: "UTC",
    dateFormat: "MM/DD/YYYY",
    timeFormat: "12h",
    dashboardLayout: "default",
    showWelcomeMessage: true,
  })

  const [isLoadingFinancial, setIsLoadingFinancial] = useState(false)
  const [isSavingFinancial, setIsSavingFinancial] = useState(false)
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false)
  const [isSavingNotifications, setIsSavingNotifications] = useState(false)
  const [isLoadingEvents, setIsLoadingEvents] = useState(false)
  const [isSavingEvents, setIsSavingEvents] = useState(false)
  const [isLoadingSecurity, setIsLoadingSecurity] = useState(false)
  const [isSavingSecurity, setIsSavingSecurity] = useState(false)
  const [isLoadingAppearance, setIsLoadingAppearance] = useState(false)
  const [isSavingAppearance, setIsSavingAppearance] = useState(false)

  // Load settings on component mount
  useEffect(() => {
    loadFinancialSettings()
    loadNotificationSettings()
    loadEventSettings()
    loadSecuritySettings()
    loadAppearanceSettings()
  }, [])

  const loadFinancialSettings = async () => {
    try {
      setIsLoadingFinancial(true)
      const settings = await apiClient.getFinancialSettings()
      
      setFinancialSettings({
        defaultProfitMargin: settings.default_profit_margin?.toString() || "25.00",
        currency: settings.currency || "XAF",
        taxRate: settings.tax_rate?.toString() || "19.25",
        serviceFeePercentage: settings.service_fee_percentage?.toString() || "5.00",
        paymentTerms: settings.payment_terms || "Net 30",
        invoicePrefix: settings.invoice_prefix || "INV",
        autoCalculateProfit: settings.auto_calculate_profit !== false,
      })
    } catch (error) {
      console.error("Failed to load financial settings:", error)
      toast.error("Failed to load financial settings")
    } finally {
      setIsLoadingFinancial(false)
    }
  }

  const loadNotificationSettings = async () => {
    try {
      setIsLoadingNotifications(true)
      const settings = await apiClient.getNotificationSettings()

      setNotifications({
        email: settings.email_notifications !== false,
        sms: settings.sms_notifications === true,
        push: settings.push_notifications !== false,
        guestCheckin: settings.guest_checkin_notifications !== false,
        vendorUpdates: settings.vendor_update_notifications !== false,
        eventReminders: settings.event_reminder_notifications !== false,
        payments: settings.payment_notifications !== false,
        system: settings.system_notifications !== false,
        marketing: settings.marketing_notifications === true,
      })
    } catch (error) {
      console.error("Failed to load notification settings:", error)
      toast.error("Failed to load notification settings")
    } finally {
      setIsLoadingNotifications(false)
    }
  }

  const saveNotificationSettings = async () => {
    try {
      setIsSavingNotifications(true)

      const settingsData = {
        email_notifications: notifications.email,
        sms_notifications: notifications.sms,
        push_notifications: notifications.push,
        guest_checkin_notifications: notifications.guestCheckin,
        vendor_update_notifications: notifications.vendorUpdates,
        event_reminder_notifications: notifications.eventReminders,
        payment_notifications: notifications.payments,
        system_notifications: notifications.system,
        marketing_notifications: notifications.marketing,
      }

      await apiClient.updateNotificationSettings(settingsData)
      toast.success("Notification settings saved successfully")
    } catch (error) {
      console.error("Failed to save notification settings:", error)
      toast.error("Failed to save notification settings")
    } finally {
      setIsSavingNotifications(false)
    }
  }

  const loadEventSettings = async () => {
    try {
      setIsLoadingEvents(true)
      const settings = await apiClient.getEventSettings()

      setEventSettings({
        defaultGuestLimit: settings.default_guest_limit?.toString() || "500",
        autoCheckin: settings.auto_checkin_enabled === true,
        qrExpiry: settings.qr_code_expiry_hours?.toString() || "24",
        allowPlusOnes: settings.allow_plus_ones !== false,
        requireRsvp: settings.require_rsvp !== false,
        defaultInvitationTemplate: settings.default_invitation_template || "traditional",
        autoSendReminders: settings.auto_send_reminders !== false,
        reminderDaysBefore: settings.reminder_days_before?.toString() || "7",
        autoGenerateQrCodes: settings.auto_generate_qr_codes !== false,
        enableDietaryRequirements: settings.enable_dietary_requirements !== false,
        enableSeatingChart: settings.enable_seating_chart === true,
        defaultEventDuration: settings.default_event_duration_hours?.toString() || "4",
        requireGuestPhone: settings.require_guest_phone === true,
        enableGuestPhotos: settings.enable_guest_photos !== false,
      })
    } catch (error) {
      console.error("Failed to load event settings:", error)
      toast.error("Failed to load event settings")
    } finally {
      setIsLoadingEvents(false)
    }
  }

  const saveEventSettings = async () => {
    try {
      setIsSavingEvents(true)

      const settingsData = {
        default_guest_limit: parseInt(eventSettings.defaultGuestLimit),
        auto_checkin_enabled: eventSettings.autoCheckin,
        qr_code_expiry_hours: parseInt(eventSettings.qrExpiry),
        allow_plus_ones: eventSettings.allowPlusOnes,
        require_rsvp: eventSettings.requireRsvp,
        default_invitation_template: eventSettings.defaultInvitationTemplate,
        auto_send_reminders: eventSettings.autoSendReminders,
        reminder_days_before: parseInt(eventSettings.reminderDaysBefore),
        auto_generate_qr_codes: eventSettings.autoGenerateQrCodes,
        enable_dietary_requirements: eventSettings.enableDietaryRequirements,
        enable_seating_chart: eventSettings.enableSeatingChart,
        default_event_duration_hours: parseInt(eventSettings.defaultEventDuration),
        require_guest_phone: eventSettings.requireGuestPhone,
        enable_guest_photos: eventSettings.enableGuestPhotos,
      }

      await apiClient.updateEventSettings(settingsData)
      toast.success("Event settings saved successfully")
    } catch (error) {
      console.error("Failed to save event settings:", error)
      toast.error("Failed to save event settings")
    } finally {
      setIsSavingEvents(false)
    }
  }

  const loadSecuritySettings = async () => {
    try {
      setIsLoadingSecurity(true)
      const settings = await apiClient.getSecuritySettings()

      setSecuritySettings({
        twoFactorEnabled: settings.two_factor_enabled === true,
        sessionTimeout: settings.session_timeout_minutes?.toString() || "480",
        passwordExpiry: settings.password_expiry_days?.toString() || "90",
        loginNotifications: settings.login_notifications !== false,
        suspiciousActivityAlerts: settings.suspicious_activity_alerts !== false,
        dataExportNotifications: settings.data_export_notifications !== false,
        accountChangesNotifications: settings.account_changes_notifications !== false,
        ipWhitelistEnabled: settings.ip_whitelist_enabled === true,
        allowedIpAddresses: settings.allowed_ip_addresses || "",
        autoLogoutInactive: settings.auto_logout_inactive !== false,
        requirePasswordChange: settings.require_password_change === true,
        backupCodesGenerated: settings.backup_codes_generated === true,
      })
    } catch (error) {
      console.error("Failed to load security settings:", error)
      toast.error("Failed to load security settings")
    } finally {
      setIsLoadingSecurity(false)
    }
  }

  const saveSecuritySettings = async () => {
    try {
      setIsSavingSecurity(true)

      const settingsData = {
        two_factor_enabled: securitySettings.twoFactorEnabled,
        session_timeout_minutes: parseInt(securitySettings.sessionTimeout),
        password_expiry_days: parseInt(securitySettings.passwordExpiry),
        login_notifications: securitySettings.loginNotifications,
        suspicious_activity_alerts: securitySettings.suspiciousActivityAlerts,
        data_export_notifications: securitySettings.dataExportNotifications,
        account_changes_notifications: securitySettings.accountChangesNotifications,
        ip_whitelist_enabled: securitySettings.ipWhitelistEnabled,
        allowed_ip_addresses: securitySettings.allowedIpAddresses,
        auto_logout_inactive: securitySettings.autoLogoutInactive,
        require_password_change: securitySettings.requirePasswordChange,
        backup_codes_generated: securitySettings.backupCodesGenerated,
      }

      await apiClient.updateSecuritySettings(settingsData)
      toast.success("Security settings saved successfully")
    } catch (error) {
      console.error("Failed to save security settings:", error)
      toast.error("Failed to save security settings")
    } finally {
      setIsSavingSecurity(false)
    }
  }

  const loadAppearanceSettings = async () => {
    try {
      setIsLoadingAppearance(true)
      const settings = await apiClient.getAppearanceSettings()

      setAppearanceSettings({
        theme: settings.theme || "light",
        primaryColor: settings.primary_color || "#3B82F6",
        sidebarCollapsed: settings.sidebar_collapsed === true,
        compactMode: settings.compact_mode === true,
        showAnimations: settings.show_animations !== false,
        fontSize: settings.font_size || "medium",
        language: settings.language || "en",
        timezone: settings.timezone || "UTC",
        dateFormat: settings.date_format || "MM/DD/YYYY",
        timeFormat: settings.time_format || "12h",
        dashboardLayout: settings.dashboard_layout || "default",
        showWelcomeMessage: settings.show_welcome_message !== false,
      })
    } catch (error) {
      console.error("Failed to load appearance settings:", error)
      toast.error("Failed to load appearance settings")
    } finally {
      setIsLoadingAppearance(false)
    }
  }

  const saveAppearanceSettings = async () => {
    try {
      setIsSavingAppearance(true)

      const settingsData = {
        theme: appearanceSettings.theme,
        primary_color: appearanceSettings.primaryColor,
        sidebar_collapsed: appearanceSettings.sidebarCollapsed,
        compact_mode: appearanceSettings.compactMode,
        show_animations: appearanceSettings.showAnimations,
        font_size: appearanceSettings.fontSize,
        language: appearanceSettings.language,
        timezone: appearanceSettings.timezone,
        date_format: appearanceSettings.dateFormat,
        time_format: appearanceSettings.timeFormat,
        dashboard_layout: appearanceSettings.dashboardLayout,
        show_welcome_message: appearanceSettings.showWelcomeMessage,
      }

      await apiClient.updateAppearanceSettings(settingsData)
      toast.success("Appearance settings saved successfully")
    } catch (error) {
      console.error("Failed to save appearance settings:", error)
      toast.error("Failed to save appearance settings")
    } finally {
      setIsSavingAppearance(false)
    }
  }

  const saveFinancialSettings = async () => {
    try {
      setIsSavingFinancial(true)

      const settingsData = {
        default_profit_margin: parseFloat(financialSettings.defaultProfitMargin),
        currency: financialSettings.currency,
        tax_rate: parseFloat(financialSettings.taxRate),
        service_fee_percentage: parseFloat(financialSettings.serviceFeePercentage),
        payment_terms: financialSettings.paymentTerms,
        invoice_prefix: financialSettings.invoicePrefix,
        auto_calculate_profit: financialSettings.autoCalculateProfit,
      }

      await apiClient.updateFinancialSettings(settingsData)
      toast.success("Financial settings saved successfully")
    } catch (error) {
      console.error("Failed to save financial settings:", error)
      toast.error("Failed to save financial settings")
    } finally {
      setIsSavingFinancial(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">Manage your account and event preferences</p>
          </div>
        </div>

        <div className="p-6">
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="events" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Events
              </TabsTrigger>
              <TabsTrigger value="financial" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Financial
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security
              </TabsTrigger>
              <TabsTrigger value="appearance" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Appearance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-600" />
                    Profile Information
                  </CardTitle>
                  <CardDescription>Update your personal information and contact details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="first-name">First Name</Label>
                      <Input id="first-name" placeholder="Enter your first name" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last-name">Last Name</Label>
                      <Input id="last-name" placeholder="Enter your last name" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input id="email" type="email" placeholder="Enter your email" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" type="tel" placeholder="Enter your phone number" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea id="bio" placeholder="Tell us about yourself" rows={4} />
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Changes</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5 text-blue-600" />
                    Notification Settings
                  </CardTitle>
                  <CardDescription>Configure how and when you receive notifications</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isLoadingNotifications ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Loading notification settings...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Communication Preferences</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="email-notifications">Email Notifications</Label>
                              <p className="text-sm text-gray-500">Receive notifications via email</p>
                            </div>
                            <Switch
                              id="email-notifications"
                              checked={notifications.email}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, email: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="sms-notifications">SMS Notifications</Label>
                              <p className="text-sm text-gray-500">Receive notifications via SMS</p>
                            </div>
                            <Switch
                              id="sms-notifications"
                              checked={notifications.sms}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, sms: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="push-notifications">Push Notifications</Label>
                              <p className="text-sm text-gray-500">Receive push notifications in your browser</p>
                            </div>
                            <Switch
                              id="push-notifications"
                              checked={notifications.push}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, push: checked })}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Event Notifications</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="guest-checkin">Guest Check-in Notifications</Label>
                              <p className="text-sm text-gray-500">Get notified when guests check in to events</p>
                            </div>
                            <Switch
                              id="guest-checkin"
                              checked={notifications.guestCheckin}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, guestCheckin: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="vendor-updates">Vendor Updates</Label>
                              <p className="text-sm text-gray-500">Get notified about vendor status changes</p>
                            </div>
                            <Switch
                              id="vendor-updates"
                              checked={notifications.vendorUpdates}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, vendorUpdates: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="event-reminders">Event Reminders</Label>
                              <p className="text-sm text-gray-500">Receive reminders about upcoming events</p>
                            </div>
                            <Switch
                              id="event-reminders"
                              checked={notifications.eventReminders}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, eventReminders: checked })}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Business Notifications</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="payment-notifications">Payment Notifications</Label>
                              <p className="text-sm text-gray-500">Get notified about payments and invoices</p>
                            </div>
                            <Switch
                              id="payment-notifications"
                              checked={notifications.payments}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, payments: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="system-notifications">System Notifications</Label>
                              <p className="text-sm text-gray-500">Important system updates and maintenance notices</p>
                            </div>
                            <Switch
                              id="system-notifications"
                              checked={notifications.system}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, system: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="marketing-notifications">Marketing Communications</Label>
                              <p className="text-sm text-gray-500">Receive updates about new features and promotions</p>
                            </div>
                            <Switch
                              id="marketing-notifications"
                              checked={notifications.marketing}
                              onCheckedChange={(checked) => setNotifications({ ...notifications, marketing: checked })}
                            />
                          </div>
                        </div>
                      </div>

                      <Button
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={saveNotificationSettings}
                        disabled={isSavingNotifications}
                      >
                        {isSavingNotifications ? "Saving..." : "Save Notification Settings"}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-blue-600" />
                    Event Settings
                  </CardTitle>
                  <CardDescription>Configure default settings for your events</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isLoadingEvents ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Loading event settings...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Guest Management</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="space-y-2">
                            <Label htmlFor="guest-limit">Default Guest Limit</Label>
                            <Input
                              id="guest-limit"
                              type="number"
                              min="1"
                              max="10000"
                              value={eventSettings.defaultGuestLimit}
                              onChange={(e) => setEventSettings({ ...eventSettings, defaultGuestLimit: e.target.value })}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="event-duration">Default Event Duration (hours)</Label>
                            <Input
                              id="event-duration"
                              type="number"
                              min="1"
                              max="24"
                              value={eventSettings.defaultEventDuration}
                              onChange={(e) => setEventSettings({ ...eventSettings, defaultEventDuration: e.target.value })}
                            />
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="allow-plus-ones">Allow Plus Ones</Label>
                              <p className="text-sm text-gray-500">Allow guests to bring additional attendees</p>
                            </div>
                            <Switch
                              id="allow-plus-ones"
                              checked={eventSettings.allowPlusOnes}
                              onCheckedChange={(checked) => setEventSettings({ ...eventSettings, allowPlusOnes: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="require-rsvp">Require RSVP</Label>
                              <p className="text-sm text-gray-500">Guests must confirm attendance</p>
                            </div>
                            <Switch
                              id="require-rsvp"
                              checked={eventSettings.requireRsvp}
                              onCheckedChange={(checked) => setEventSettings({ ...eventSettings, requireRsvp: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="require-phone">Require Guest Phone</Label>
                              <p className="text-sm text-gray-500">Make phone number mandatory for guests</p>
                            </div>
                            <Switch
                              id="require-phone"
                              checked={eventSettings.requireGuestPhone}
                              onCheckedChange={(checked) => setEventSettings({ ...eventSettings, requireGuestPhone: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="dietary-requirements">Enable Dietary Requirements</Label>
                              <p className="text-sm text-gray-500">Allow guests to specify dietary needs</p>
                            </div>
                            <Switch
                              id="dietary-requirements"
                              checked={eventSettings.enableDietaryRequirements}
                              onCheckedChange={(checked) => setEventSettings({ ...eventSettings, enableDietaryRequirements: checked })}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Check-in & QR Codes</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="auto-checkin">Auto Check-in</Label>
                              <p className="text-sm text-gray-500">Automatically check in guests when they scan QR codes</p>
                            </div>
                            <Switch
                              id="auto-checkin"
                              checked={eventSettings.autoCheckin}
                              onCheckedChange={(checked) => setEventSettings({ ...eventSettings, autoCheckin: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="auto-generate-qr">Auto Generate QR Codes</Label>
                              <p className="text-sm text-gray-500">Automatically generate QR codes for new guests</p>
                            </div>
                            <Switch
                              id="auto-generate-qr"
                              checked={eventSettings.autoGenerateQrCodes}
                              onCheckedChange={(checked) => setEventSettings({ ...eventSettings, autoGenerateQrCodes: checked })}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="qr-expiry">QR Code Expiry (hours)</Label>
                            <Select
                              value={eventSettings.qrExpiry}
                              onValueChange={(value) => setEventSettings({ ...eventSettings, qrExpiry: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">1 hour</SelectItem>
                                <SelectItem value="6">6 hours</SelectItem>
                                <SelectItem value="12">12 hours</SelectItem>
                                <SelectItem value="24">24 hours</SelectItem>
                                <SelectItem value="48">48 hours</SelectItem>
                                <SelectItem value="72">72 hours</SelectItem>
                                <SelectItem value="168">1 week</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Event Features</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="seating-chart">Enable Seating Chart</Label>
                              <p className="text-sm text-gray-500">Allow table and seat assignments</p>
                            </div>
                            <Switch
                              id="seating-chart"
                              checked={eventSettings.enableSeatingChart}
                              onCheckedChange={(checked) => setEventSettings({ ...eventSettings, enableSeatingChart: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="guest-photos">Enable Guest Photos</Label>
                              <p className="text-sm text-gray-500">Allow photo uploads for guests</p>
                            </div>
                            <Switch
                              id="guest-photos"
                              checked={eventSettings.enableGuestPhotos}
                              onCheckedChange={(checked) => setEventSettings({ ...eventSettings, enableGuestPhotos: checked })}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="invitation-template">Default Invitation Template</Label>
                            <Select
                              value={eventSettings.defaultInvitationTemplate}
                              onValueChange={(value) => setEventSettings({ ...eventSettings, defaultInvitationTemplate: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="traditional">Traditional</SelectItem>
                                <SelectItem value="modern">Modern</SelectItem>
                                <SelectItem value="elegant">Elegant</SelectItem>
                                <SelectItem value="casual">Casual</SelectItem>
                                <SelectItem value="corporate">Corporate</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>

                      <Button
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={saveEventSettings}
                        disabled={isSavingEvents}
                      >
                        {isSavingEvents ? "Saving..." : "Save Event Settings"}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-blue-600" />
                    Security Settings
                  </CardTitle>
                  <CardDescription>Manage your account security and privacy settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isLoadingSecurity ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Loading security settings...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Authentication</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="two-factor">Two-Factor Authentication</Label>
                              <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
                            </div>
                            <Switch
                              id="two-factor"
                              checked={securitySettings.twoFactorEnabled}
                              onCheckedChange={(checked) => setSecuritySettings({ ...securitySettings, twoFactorEnabled: checked })}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                              <Input
                                id="session-timeout"
                                type="number"
                                min="5"
                                max="1440"
                                value={securitySettings.sessionTimeout}
                                onChange={(e) => setSecuritySettings({ ...securitySettings, sessionTimeout: e.target.value })}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="password-expiry">Password Expiry (days)</Label>
                              <Input
                                id="password-expiry"
                                type="number"
                                min="30"
                                max="365"
                                value={securitySettings.passwordExpiry}
                                onChange={(e) => setSecuritySettings({ ...securitySettings, passwordExpiry: e.target.value })}
                              />
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="auto-logout">Auto Logout When Inactive</Label>
                              <p className="text-sm text-gray-500">Automatically log out after session timeout</p>
                            </div>
                            <Switch
                              id="auto-logout"
                              checked={securitySettings.autoLogoutInactive}
                              onCheckedChange={(checked) => setSecuritySettings({ ...securitySettings, autoLogoutInactive: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="require-password-change">Require Password Change</Label>
                              <p className="text-sm text-gray-500">Force password change on next login</p>
                            </div>
                            <Switch
                              id="require-password-change"
                              checked={securitySettings.requirePasswordChange}
                              onCheckedChange={(checked) => setSecuritySettings({ ...securitySettings, requirePasswordChange: checked })}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Security Notifications</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="login-notifications">Login Notifications</Label>
                              <p className="text-sm text-gray-500">Get notified of new login attempts</p>
                            </div>
                            <Switch
                              id="login-notifications"
                              checked={securitySettings.loginNotifications}
                              onCheckedChange={(checked) => setSecuritySettings({ ...securitySettings, loginNotifications: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="suspicious-activity">Suspicious Activity Alerts</Label>
                              <p className="text-sm text-gray-500">Get alerts for unusual account activity</p>
                            </div>
                            <Switch
                              id="suspicious-activity"
                              checked={securitySettings.suspiciousActivityAlerts}
                              onCheckedChange={(checked) => setSecuritySettings({ ...securitySettings, suspiciousActivityAlerts: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="account-changes">Account Changes Notifications</Label>
                              <p className="text-sm text-gray-500">Get notified when account settings change</p>
                            </div>
                            <Switch
                              id="account-changes"
                              checked={securitySettings.accountChangesNotifications}
                              onCheckedChange={(checked) => setSecuritySettings({ ...securitySettings, accountChangesNotifications: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="data-export">Data Export Notifications</Label>
                              <p className="text-sm text-gray-500">Get notified when data is exported</p>
                            </div>
                            <Switch
                              id="data-export"
                              checked={securitySettings.dataExportNotifications}
                              onCheckedChange={(checked) => setSecuritySettings({ ...securitySettings, dataExportNotifications: checked })}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Access Control</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="ip-whitelist">IP Address Whitelist</Label>
                              <p className="text-sm text-gray-500">Restrict access to specific IP addresses</p>
                            </div>
                            <Switch
                              id="ip-whitelist"
                              checked={securitySettings.ipWhitelistEnabled}
                              onCheckedChange={(checked) => setSecuritySettings({ ...securitySettings, ipWhitelistEnabled: checked })}
                            />
                          </div>

                          {securitySettings.ipWhitelistEnabled && (
                            <div className="space-y-2">
                              <Label htmlFor="allowed-ips">Allowed IP Addresses</Label>
                              <Textarea
                                id="allowed-ips"
                                placeholder="Enter IP addresses, one per line (e.g., ***********)"
                                rows={4}
                                value={securitySettings.allowedIpAddresses}
                                onChange={(e) => setSecuritySettings({ ...securitySettings, allowedIpAddresses: e.target.value })}
                              />
                            </div>
                          )}
                        </div>
                      </div>

                      <Button
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={saveSecuritySettings}
                        disabled={isSavingSecurity}
                      >
                        {isSavingSecurity ? "Saving..." : "Save Security Settings"}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="financial" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-blue-600" />
                    Financial Settings
                  </CardTitle>
                  <CardDescription>Configure your profit margins, pricing, and financial preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isLoadingFinancial ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Loading financial settings...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="profit-margin">Default Profit Margin (%)</Label>
                          <Input
                            id="profit-margin"
                            type="number"
                            step="0.01"
                            min="0"
                            max="100"
                            value={financialSettings.defaultProfitMargin}
                            onChange={(e) => setFinancialSettings({ ...financialSettings, defaultProfitMargin: e.target.value })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="currency">Currency</Label>
                          <Select
                            value={financialSettings.currency}
                            onValueChange={(value) => setFinancialSettings({ ...financialSettings, currency: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="XAF">XAF (Central African Franc)</SelectItem>
                              <SelectItem value="USD">USD (US Dollar)</SelectItem>
                              <SelectItem value="EUR">EUR (Euro)</SelectItem>
                              <SelectItem value="GBP">GBP (British Pound)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <Button 
                        className="bg-blue-600 hover:bg-blue-700" 
                        onClick={saveFinancialSettings}
                        disabled={isSavingFinancial}
                      >
                        {isSavingFinancial ? "Saving..." : "Save Financial Settings"}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="appearance" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5 text-blue-600" />
                    Appearance Settings
                  </CardTitle>
                  <CardDescription>Customize the look and feel of your dashboard</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isLoadingAppearance ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Loading appearance settings...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Theme & Colors</h3>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="theme">Theme</Label>
                            <Select
                              value={appearanceSettings.theme}
                              onValueChange={(value) => setAppearanceSettings({ ...appearanceSettings, theme: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="light">Light</SelectItem>
                                <SelectItem value="dark">Dark</SelectItem>
                                <SelectItem value="system">System</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="primary-color">Primary Color</Label>
                            <div className="flex items-center gap-4">
                              <Input
                                id="primary-color"
                                type="color"
                                value={appearanceSettings.primaryColor}
                                onChange={(e) => setAppearanceSettings({ ...appearanceSettings, primaryColor: e.target.value })}
                                className="w-20 h-10"
                              />
                              <Input
                                type="text"
                                value={appearanceSettings.primaryColor}
                                onChange={(e) => setAppearanceSettings({ ...appearanceSettings, primaryColor: e.target.value })}
                                placeholder="#3B82F6"
                                className="flex-1"
                              />
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="show-animations">Show Animations</Label>
                              <p className="text-sm text-gray-500">Enable smooth transitions and animations</p>
                            </div>
                            <Switch
                              id="show-animations"
                              checked={appearanceSettings.showAnimations}
                              onCheckedChange={(checked) => setAppearanceSettings({ ...appearanceSettings, showAnimations: checked })}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Layout & Display</h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="sidebar-collapsed">Collapsed Sidebar</Label>
                              <p className="text-sm text-gray-500">Start with sidebar collapsed by default</p>
                            </div>
                            <Switch
                              id="sidebar-collapsed"
                              checked={appearanceSettings.sidebarCollapsed}
                              onCheckedChange={(checked) => setAppearanceSettings({ ...appearanceSettings, sidebarCollapsed: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="compact-mode">Compact Mode</Label>
                              <p className="text-sm text-gray-500">Reduce spacing for more content on screen</p>
                            </div>
                            <Switch
                              id="compact-mode"
                              checked={appearanceSettings.compactMode}
                              onCheckedChange={(checked) => setAppearanceSettings({ ...appearanceSettings, compactMode: checked })}
                            />
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <Label htmlFor="welcome-message">Show Welcome Message</Label>
                              <p className="text-sm text-gray-500">Display welcome message on dashboard</p>
                            </div>
                            <Switch
                              id="welcome-message"
                              checked={appearanceSettings.showWelcomeMessage}
                              onCheckedChange={(checked) => setAppearanceSettings({ ...appearanceSettings, showWelcomeMessage: checked })}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="font-size">Font Size</Label>
                              <Select
                                value={appearanceSettings.fontSize}
                                onValueChange={(value) => setAppearanceSettings({ ...appearanceSettings, fontSize: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="small">Small</SelectItem>
                                  <SelectItem value="medium">Medium</SelectItem>
                                  <SelectItem value="large">Large</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="dashboard-layout">Dashboard Layout</Label>
                              <Select
                                value={appearanceSettings.dashboardLayout}
                                onValueChange={(value) => setAppearanceSettings({ ...appearanceSettings, dashboardLayout: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="default">Default</SelectItem>
                                  <SelectItem value="compact">Compact</SelectItem>
                                  <SelectItem value="detailed">Detailed</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="text-lg font-medium text-gray-900">Localization</h3>
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="language">Language</Label>
                              <Select
                                value={appearanceSettings.language}
                                onValueChange={(value) => setAppearanceSettings({ ...appearanceSettings, language: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="en">English</SelectItem>
                                  <SelectItem value="fr">Français</SelectItem>
                                  <SelectItem value="es">Español</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="timezone">Timezone</Label>
                              <Select
                                value={appearanceSettings.timezone}
                                onValueChange={(value) => setAppearanceSettings({ ...appearanceSettings, timezone: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="UTC">UTC</SelectItem>
                                  <SelectItem value="America/New_York">Eastern Time</SelectItem>
                                  <SelectItem value="America/Chicago">Central Time</SelectItem>
                                  <SelectItem value="America/Denver">Mountain Time</SelectItem>
                                  <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                                  <SelectItem value="Europe/London">London</SelectItem>
                                  <SelectItem value="Europe/Paris">Paris</SelectItem>
                                  <SelectItem value="Africa/Douala">Douala</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                              <Label htmlFor="date-format">Date Format</Label>
                              <Select
                                value={appearanceSettings.dateFormat}
                                onValueChange={(value) => setAppearanceSettings({ ...appearanceSettings, dateFormat: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                                  <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                                  <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="time-format">Time Format</Label>
                              <Select
                                value={appearanceSettings.timeFormat}
                                onValueChange={(value) => setAppearanceSettings({ ...appearanceSettings, timeFormat: value })}
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="12h">12 Hour</SelectItem>
                                  <SelectItem value="24h">24 Hour</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      </div>

                      <Button
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={saveAppearanceSettings}
                        disabled={isSavingAppearance}
                      >
                        {isSavingAppearance ? "Saving..." : "Save Appearance Settings"}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
