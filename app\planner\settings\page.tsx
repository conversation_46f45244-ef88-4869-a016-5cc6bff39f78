"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { User, Bell, Shield, Palette, Calendar, DollarSign } from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export default function PlannerSettings() {
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    guestCheckin: true,
    vendorUpdates: true,
  })

  const [eventSettings, setEventSettings] = useState({
    autoCheckin: false,
    qrExpiry: "24",
    guestLimit: "500",
    allowPlusOnes: true,
  })

  const [financialSettings, setFinancialSettings] = useState({
    defaultProfitMargin: "25.00",
    currency: "XAF",
    taxRate: "19.25",
    serviceFeePercentage: "5.00",
    paymentTerms: "Net 30",
    invoicePrefix: "INV",
    autoCalculateProfit: true,
  })

  const [isLoadingFinancial, setIsLoadingFinancial] = useState(false)
  const [isSavingFinancial, setIsSavingFinancial] = useState(false)

  // Load financial settings on component mount
  useEffect(() => {
    loadFinancialSettings()
  }, [])

  const loadFinancialSettings = async () => {
    try {
      setIsLoadingFinancial(true)
      const settings = await apiClient.getFinancialSettings()

      setFinancialSettings({
        defaultProfitMargin: settings.default_profit_margin?.toString() || "25.00",
        currency: settings.currency || "XAF",
        taxRate: settings.tax_rate?.toString() || "19.25",
        serviceFeePercentage: settings.service_fee_percentage?.toString() || "5.00",
        paymentTerms: settings.payment_terms || "Net 30",
        invoicePrefix: settings.invoice_prefix || "INV",
        autoCalculateProfit: settings.auto_calculate_profit !== false,
      })
    } catch (error) {
      console.error("Failed to load financial settings:", error)
      toast.error("Failed to load financial settings")
    } finally {
      setIsLoadingFinancial(false)
    }
  }

  const saveFinancialSettings = async () => {
    try {
      setIsSavingFinancial(true)

      const settingsData = {
        default_profit_margin: parseFloat(financialSettings.defaultProfitMargin),
        currency: financialSettings.currency,
        tax_rate: parseFloat(financialSettings.taxRate),
        service_fee_percentage: parseFloat(financialSettings.serviceFeePercentage),
        payment_terms: financialSettings.paymentTerms,
        invoice_prefix: financialSettings.invoicePrefix,
        auto_calculate_profit: financialSettings.autoCalculateProfit,
      }

      await apiClient.updateFinancialSettings(settingsData)
      toast.success("Financial settings saved successfully")
    } catch (error) {
      console.error("Failed to save financial settings:", error)
      toast.error("Failed to save financial settings")
    } finally {
      setIsSavingFinancial(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">Manage your account and event preferences</p>
          </div>
        </div>

        <div className="p-6">
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="events" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Events
              </TabsTrigger>
              <TabsTrigger value="financial" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Financial
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security
              </TabsTrigger>
              <TabsTrigger value="appearance" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Appearance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-600" />
                    Profile Information
                  </CardTitle>
                  <CardDescription>Update your personal information and contact details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input id="firstName" defaultValue="John" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input id="lastName" defaultValue="Doe" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input id="email" type="email" defaultValue="<EMAIL>" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" type="tel" defaultValue="+****************" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company">Company</Label>
                    <Input id="company" defaultValue="Event Planning Co." />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea id="bio" placeholder="Tell us about yourself..." rows={4} />
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Changes</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5 text-blue-600" />
                    Notification Preferences
                  </CardTitle>
                  <CardDescription>Choose how you want to be notified about events and updates</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="email-notifications">Email Notifications</Label>
                        <p className="text-sm text-gray-500">Receive notifications via email</p>
                      </div>
                      <Switch
                        id="email-notifications"
                        checked={notifications.email}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, email: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="sms-notifications">SMS Notifications</Label>
                        <p className="text-sm text-gray-500">Receive notifications via text message</p>
                      </div>
                      <Switch
                        id="sms-notifications"
                        checked={notifications.sms}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, sms: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="push-notifications">Push Notifications</Label>
                        <p className="text-sm text-gray-500">Receive push notifications in your browser</p>
                      </div>
                      <Switch
                        id="push-notifications"
                        checked={notifications.push}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, push: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="guest-checkin">Guest Check-in Alerts</Label>
                        <p className="text-sm text-gray-500">Get notified when guests check in</p>
                      </div>
                      <Switch
                        id="guest-checkin"
                        checked={notifications.guestCheckin}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, guestCheckin: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="vendor-updates">Vendor Updates</Label>
                        <p className="text-sm text-gray-500">Receive updates from event vendors</p>
                      </div>
                      <Switch
                        id="vendor-updates"
                        checked={notifications.vendorUpdates}
                        onCheckedChange={(checked) => setNotifications({ ...notifications, vendorUpdates: checked })}
                      />
                    </div>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Preferences</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="events" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-blue-600" />
                    Event Settings
                  </CardTitle>
                  <CardDescription>Configure default settings for your events</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-checkin">Auto Check-in</Label>
                        <p className="text-sm text-gray-500">Automatically check in guests when they scan QR codes</p>
                      </div>
                      <Switch
                        id="auto-checkin"
                        checked={eventSettings.autoCheckin}
                        onCheckedChange={(checked) => setEventSettings({ ...eventSettings, autoCheckin: checked })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="qr-expiry">QR Code Expiry (hours)</Label>
                      <Select
                        value={eventSettings.qrExpiry}
                        onValueChange={(value) => setEventSettings({ ...eventSettings, qrExpiry: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 hour</SelectItem>
                          <SelectItem value="6">6 hours</SelectItem>
                          <SelectItem value="12">12 hours</SelectItem>
                          <SelectItem value="24">24 hours</SelectItem>
                          <SelectItem value="48">48 hours</SelectItem>
                          <SelectItem value="never">Never expire</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="guest-limit">Default Guest Limit</Label>
                      <Input
                        id="guest-limit"
                        type="number"
                        value={eventSettings.guestLimit}
                        onChange={(e) => setEventSettings({ ...eventSettings, guestLimit: e.target.value })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="plus-ones">Allow Plus Ones</Label>
                        <p className="text-sm text-gray-500">Allow guests to bring additional attendees</p>
                      </div>
                      <Switch
                        id="plus-ones"
                        checked={eventSettings.allowPlusOnes}
                        onCheckedChange={(checked) => setEventSettings({ ...eventSettings, allowPlusOnes: checked })}
                      />
                    </div>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Settings</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="financial" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-blue-600" />
                    Financial Settings
                  </CardTitle>
                  <CardDescription>Configure your profit margins, pricing, and financial preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isLoadingFinancial ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Loading financial settings...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="profit-margin">Default Profit Margin (%)</Label>
                        <Input
                          id="profit-margin"
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          value={financialSettings.defaultProfitMargin}
                          onChange={(e) => setFinancialSettings({ ...financialSettings, defaultProfitMargin: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="currency">Currency</Label>
                        <Select
                          value={financialSettings.currency}
                          onValueChange={(value) => setFinancialSettings({ ...financialSettings, currency: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="XAF">XAF (Central African Franc)</SelectItem>
                            <SelectItem value="USD">USD (US Dollar)</SelectItem>
                            <SelectItem value="EUR">EUR (Euro)</SelectItem>
                            <SelectItem value="GBP">GBP (British Pound)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="tax-rate">Tax Rate (%)</Label>
                        <Input
                          id="tax-rate"
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          value={financialSettings.taxRate}
                          onChange={(e) => setFinancialSettings({ ...financialSettings, taxRate: e.target.value })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="service-fee">Service Fee (%)</Label>
                        <Input
                          id="service-fee"
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          value={financialSettings.serviceFeePercentage}
                          onChange={(e) => setFinancialSettings({ ...financialSettings, serviceFeePercentage: e.target.value })}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="payment-terms">Payment Terms</Label>
                        <Select
                          value={financialSettings.paymentTerms}
                          onValueChange={(value) => setFinancialSettings({ ...financialSettings, paymentTerms: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Net 15">Net 15 days</SelectItem>
                            <SelectItem value="Net 30">Net 30 days</SelectItem>
                            <SelectItem value="Net 45">Net 45 days</SelectItem>
                            <SelectItem value="Net 60">Net 60 days</SelectItem>
                            <SelectItem value="Due on Receipt">Due on Receipt</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="invoice-prefix">Invoice Prefix</Label>
                        <Input
                          id="invoice-prefix"
                          placeholder="e.g., INV, BA-, TE-"
                          value={financialSettings.invoicePrefix}
                          onChange={(e) => setFinancialSettings({ ...financialSettings, invoicePrefix: e.target.value })}
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-calculate">Auto Calculate Profit</Label>
                        <p className="text-sm text-gray-500">Automatically calculate profit based on costs and margin</p>
                      </div>
                      <Switch
                        id="auto-calculate"
                        checked={financialSettings.autoCalculateProfit}
                        onCheckedChange={(checked) => setFinancialSettings({ ...financialSettings, autoCalculateProfit: checked })}
                      />
                    </div>
                  </div>

                    <Button
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={saveFinancialSettings}
                      disabled={isSavingFinancial}
                    >
                      {isSavingFinancial ? "Saving..." : "Save Financial Settings"}
                    </Button>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-blue-600" />
                    Security Settings
                  </CardTitle>
                  <CardDescription>Manage your account security and privacy</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input id="current-password" type="password" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input id="new-password" type="password" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirm-password">Confirm New Password</Label>
                      <Input id="confirm-password" type="password" />
                    </div>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Update Password</Button>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Two-Factor Authentication</h3>
                    <p className="text-gray-600 mb-4">Add an extra layer of security to your account</p>
                    <Button variant="outline">Enable 2FA</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="appearance" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5 text-blue-600" />
                    Appearance Settings
                  </CardTitle>
                  <CardDescription>Customize the look and feel of your dashboard</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Theme</Label>
                      <Select defaultValue="light">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                          <SelectItem value="system">System</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Language</Label>
                      <Select defaultValue="en">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Timezone</Label>
                      <Select defaultValue="utc">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="utc">UTC</SelectItem>
                          <SelectItem value="est">Eastern Time</SelectItem>
                          <SelectItem value="pst">Pacific Time</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Preferences</Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
