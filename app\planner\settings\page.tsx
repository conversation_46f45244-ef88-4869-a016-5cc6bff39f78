"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { User, Bell, Shield, Palette, Calendar, DollarSign } from "lucide-react"
import { PlannerSidebar } from "@/components/planner-sidebar"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export default function PlannerSettings() {
  const [notifications, setNotifications] = useState({
    email: true,
    sms: false,
    push: true,
    guestCheckin: true,
    vendorUpdates: true,
  })

  const [eventSettings, setEventSettings] = useState({
    autoCheckin: false,
    qrExpiry: "24",
    guestLimit: "500",
    allowPlusOnes: true,
  })

  const [financialSettings, setFinancialSettings] = useState({
    defaultProfitMargin: "25.00",
    currency: "XAF",
    taxRate: "19.25",
    serviceFeePercentage: "5.00",
    paymentTerms: "Net 30",
    invoicePrefix: "INV",
    autoCalculateProfit: true,
  })

  const [isLoadingFinancial, setIsLoadingFinancial] = useState(false)
  const [isSavingFinancial, setIsSavingFinancial] = useState(false)

  // Load financial settings on component mount
  useEffect(() => {
    loadFinancialSettings()
  }, [])

  const loadFinancialSettings = async () => {
    try {
      setIsLoadingFinancial(true)
      const settings = await apiClient.getFinancialSettings()
      
      setFinancialSettings({
        defaultProfitMargin: settings.default_profit_margin?.toString() || "25.00",
        currency: settings.currency || "XAF",
        taxRate: settings.tax_rate?.toString() || "19.25",
        serviceFeePercentage: settings.service_fee_percentage?.toString() || "5.00",
        paymentTerms: settings.payment_terms || "Net 30",
        invoicePrefix: settings.invoice_prefix || "INV",
        autoCalculateProfit: settings.auto_calculate_profit !== false,
      })
    } catch (error) {
      console.error("Failed to load financial settings:", error)
      toast.error("Failed to load financial settings")
    } finally {
      setIsLoadingFinancial(false)
    }
  }

  const saveFinancialSettings = async () => {
    try {
      setIsSavingFinancial(true)
      
      const settingsData = {
        default_profit_margin: parseFloat(financialSettings.defaultProfitMargin),
        currency: financialSettings.currency,
        tax_rate: parseFloat(financialSettings.taxRate),
        service_fee_percentage: parseFloat(financialSettings.serviceFeePercentage),
        payment_terms: financialSettings.paymentTerms,
        invoice_prefix: financialSettings.invoicePrefix,
        auto_calculate_profit: financialSettings.autoCalculateProfit,
      }

      await apiClient.updateFinancialSettings(settingsData)
      toast.success("Financial settings saved successfully")
    } catch (error) {
      console.error("Failed to save financial settings:", error)
      toast.error("Failed to save financial settings")
    } finally {
      setIsSavingFinancial(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <PlannerSidebar />

      <div className="flex-1 overflow-auto">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">Manage your account and event preferences</p>
          </div>
        </div>

        <div className="p-6">
          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="events" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Events
              </TabsTrigger>
              <TabsTrigger value="financial" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Financial
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security
              </TabsTrigger>
              <TabsTrigger value="appearance" className="flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Appearance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-600" />
                    Profile Information
                  </CardTitle>
                  <CardDescription>Update your personal information and contact details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="first-name">First Name</Label>
                      <Input id="first-name" placeholder="Enter your first name" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last-name">Last Name</Label>
                      <Input id="last-name" placeholder="Enter your last name" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input id="email" type="email" placeholder="Enter your email" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" type="tel" placeholder="Enter your phone number" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea id="bio" placeholder="Tell us about yourself" rows={4} />
                  </div>

                  <Button className="bg-blue-600 hover:bg-blue-700">Save Changes</Button>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="financial" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-blue-600" />
                    Financial Settings
                  </CardTitle>
                  <CardDescription>Configure your profit margins, pricing, and financial preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {isLoadingFinancial ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Loading financial settings...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="profit-margin">Default Profit Margin (%)</Label>
                          <Input
                            id="profit-margin"
                            type="number"
                            step="0.01"
                            min="0"
                            max="100"
                            value={financialSettings.defaultProfitMargin}
                            onChange={(e) => setFinancialSettings({ ...financialSettings, defaultProfitMargin: e.target.value })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="currency">Currency</Label>
                          <Select
                            value={financialSettings.currency}
                            onValueChange={(value) => setFinancialSettings({ ...financialSettings, currency: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="XAF">XAF (Central African Franc)</SelectItem>
                              <SelectItem value="USD">USD (US Dollar)</SelectItem>
                              <SelectItem value="EUR">EUR (Euro)</SelectItem>
                              <SelectItem value="GBP">GBP (British Pound)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <Button 
                        className="bg-blue-600 hover:bg-blue-700" 
                        onClick={saveFinancialSettings}
                        disabled={isSavingFinancial}
                      >
                        {isSavingFinancial ? "Saving..." : "Save Financial Settings"}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
