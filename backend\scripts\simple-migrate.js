const mysql = require("mysql2/promise")
require("dotenv").config()

async function runSimpleMigration() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
  })

  try {
    console.log("Creating database...")
    await connection.execute("CREATE DATABASE IF NOT EXISTS otantik_ems")
    console.log("✅ Database created!")

    // Close and reconnect to the specific database
    await connection.end()

    const dbConnection = await mysql.createConnection({
      host: process.env.DB_HOST || "localhost",
      user: process.env.DB_USER || "root",
      password: process.env.DB_PASSWORD || "",
      database: "otantik_ems",
    })

    console.log("Creating tables...")

    // Create events table first (needed for foreign key)
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS events (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        event_date DATE NOT NULL,
        event_time TIME NOT NULL,
        venue VARCHAR(255) NOT NULL,
        address TEXT,
        region VARCHAR(100),
        expected_guests INT DEFAULT 0,
        budget DECIMAL(15,2),
        planner_id INT,
        status ENUM('planning', 'confirmed', 'completed', 'cancelled') DEFAULT 'planning',
        invitation_template VARCHAR(50) DEFAULT 'traditional',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `)
    console.log("✅ Events table created!")

    // Create guests table
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS guests (
        id INT PRIMARY KEY AUTO_INCREMENT,
        event_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(20),
        table_number VARCHAR(50),
        seat_number VARCHAR(50),
        rsvp_status ENUM('pending', 'confirmed', 'declined') DEFAULT 'pending',
        checked_in BOOLEAN DEFAULT FALSE,
        check_in_time TIMESTAMP NULL,
        whatsapp_sent BOOLEAN DEFAULT FALSE,
        whatsapp_sent_at TIMESTAMP NULL,
        qr_code VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
      )
    `)
    console.log("✅ Guests table created!")

    // Insert a sample event for testing
    await dbConnection.execute(`
      INSERT IGNORE INTO events (id, title, description, event_date, event_time, venue, address, expected_guests)
      VALUES (1, 'Sample Wedding Event', 'A beautiful wedding celebration', '2024-12-31', '18:00:00', 'Grand Ballroom', '123 Event Street, City', 100)
    `)
    console.log("✅ Sample event created!")

    await dbConnection.end()
    console.log("✅ Database setup completed successfully!")

  } catch (error) {
    console.error("❌ Migration failed:", error)
    process.exit(1)
  }
}

runSimpleMigration()
