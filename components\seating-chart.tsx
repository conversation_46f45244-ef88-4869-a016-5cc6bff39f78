"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Layout, Users, Plus, RotateCcw, Save, Edit, Trash2, UserPlus, Settings } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface Guest {
  id: number
  name: string
  email: string
  phone: string
  table_number: string
  seat_number: string
  rsvp_status: string
}

interface Table {
  id: number
  number: string
  seats: number
  x: number
  y: number
  shape: 'round' | 'rectangle'
  guests: Guest[]
}

export function Seating<PERSON>hart() {
  const [tables, setTables] = useState<Table[]>([])
  const [guests, setGuests] = useState<Guest[]>([])
  const [selectedTable, setSelectedTable] = useState<Table | null>(null)
  const [showAddTableDialog, setShowAddTableDialog] = useState(false)
  const [showTableDetailsDialog, setShowTableDetailsDialog] = useState(false)
  const [showGuestAssignDialog, setShowGuestAssignDialog] = useState(false)
  const [draggedTable, setDraggedTable] = useState<number | null>(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [newTable, setNewTable] = useState({
    number: '',
    seats: 8,
    shape: 'round' as 'round' | 'rectangle'
  })
  const [unassignedGuests, setUnassignedGuests] = useState<Guest[]>([])
  const canvasRef = useRef<HTMLDivElement>(null)
  const eventId = 1 // TODO: Get from props/context

  // Fetch guests and tables data
  const fetchData = useCallback(async () => {
    try {
      // Fetch guests
      const guestsResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests`, {
        credentials: 'include'
      })
      if (guestsResponse.ok) {
        const guestsData = await guestsResponse.json()
        setGuests(guestsData)

        // Group guests by table and create table structure
        const tableMap = new Map<string, Guest[]>()
        const unassigned: Guest[] = []

        guestsData.forEach((guest: Guest) => {
          if (guest.table_number && guest.table_number.trim()) {
            const tableKey = guest.table_number.trim()
            if (!tableMap.has(tableKey)) {
              tableMap.set(tableKey, [])
            }
            tableMap.get(tableKey)!.push(guest)
          } else {
            unassigned.push(guest)
          }
        })

        // Create tables from guest data
        const tablesData: Table[] = []
        let tableId = 1
        tableMap.forEach((tableGuests, tableNumber) => {
          tablesData.push({
            id: tableId++,
            number: tableNumber,
            seats: Math.max(8, tableGuests.length), // Default to 8 or guest count
            x: 100 + (tablesData.length % 4) * 150,
            y: 100 + Math.floor(tablesData.length / 4) * 150,
            shape: 'round',
            guests: tableGuests
          })
        })

        setTables(tablesData)
        setUnassignedGuests(unassigned)
      }
    } catch (error) {
      console.error('Failed to fetch data:', error)
      toast({
        title: "Error",
        description: "Failed to load seating data",
        variant: "destructive"
      })
    }
  }, [eventId])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Drag and drop handlers
  const handleMouseDown = (e: React.MouseEvent, tableId: number) => {
    const table = tables.find(t => t.id === tableId)
    if (!table) return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    setDraggedTable(tableId)
    setDragOffset({
      x: e.clientX - rect.left - table.x,
      y: e.clientY - rect.top - table.y
    })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (draggedTable === null) return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const newX = e.clientX - rect.left - dragOffset.x
    const newY = e.clientY - rect.top - dragOffset.y

    setTables(tables.map(table =>
      table.id === draggedTable
        ? { ...table, x: Math.max(0, newX), y: Math.max(0, newY) }
        : table
    ))
  }

  const handleMouseUp = () => {
    setDraggedTable(null)
  }

  // Table management functions
  const addTable = async () => {
    if (!newTable.number.trim()) {
      toast({
        title: "Validation Error",
        description: "Table number is required",
        variant: "destructive"
      })
      return
    }

    const tableExists = tables.some(t => t.number === newTable.number.trim())
    if (tableExists) {
      toast({
        title: "Validation Error",
        description: "Table number already exists",
        variant: "destructive"
      })
      return
    }

    const newTableData: Table = {
      id: Math.max(...tables.map(t => t.id), 0) + 1,
      number: newTable.number.trim(),
      seats: newTable.seats,
      x: 100,
      y: 100,
      shape: newTable.shape,
      guests: []
    }

    setTables([...tables, newTableData])
    setShowAddTableDialog(false)
    setNewTable({ number: '', seats: 8, shape: 'round' })

    toast({
      title: "Table Added",
      description: `Table ${newTable.number} has been added`
    })
  }

  const deleteTable = (tableId: number) => {
    const table = tables.find(t => t.id === tableId)
    if (!table) return

    if (table.guests.length > 0) {
      toast({
        title: "Cannot Delete Table",
        description: "Please reassign guests before deleting this table",
        variant: "destructive"
      })
      return
    }

    setTables(tables.filter(t => t.id !== tableId))
    toast({
      title: "Table Deleted",
      description: `Table ${table.number} has been deleted`
    })
  }

  const assignGuestToTable = async (guestId: number, tableNumber: string) => {
    try {
      const guest = guests.find(g => g.id === guestId)
      if (!guest) return

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api/events/${eventId}/guests/${guestId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          name: guest.name,
          email: guest.email,
          phone: guest.phone,
          table_number: tableNumber || null,
          seat_number: guest.seat_number
        })
      })

      if (response.ok) {
        await fetchData() // Refresh data
        toast({
          title: tableNumber ? "Guest Assigned" : "Guest Unassigned",
          description: tableNumber
            ? `${guest.name} has been assigned to ${tableNumber}`
            : `${guest.name} has been removed from their table`
        })
      }
    } catch (error) {
      toast({
        title: "Assignment Failed",
        description: "Failed to update guest assignment",
        variant: "destructive"
      })
    }
  }

  const saveLayout = async () => {
    // TODO: Implement save layout to backend
    toast({
      title: "Layout Saved",
      description: "Seating arrangement has been saved"
    })
  }

  const resetLayout = () => {
    fetchData() // Reload original data
    toast({
      title: "Layout Reset",
      description: "Seating arrangement has been reset"
    })
  }

  return (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Layout className="h-5 w-5 text-purple-600" />
                Seating Chart
              </CardTitle>
              <CardDescription>Arrange guest seating for your event</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAddTableDialog(true)}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Table
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={resetLayout}
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Reset
              </Button>
              <Button
                size="sm"
                className="bg-gradient-to-r from-purple-600 to-pink-600"
                onClick={saveLayout}
              >
                <Save className="h-4 w-4 mr-1" />
                Save Layout
              </Button>
              {unassignedGuests.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowGuestAssignDialog(true)}
                  className="bg-amber-50 border-amber-200 text-amber-700"
                >
                  <UserPlus className="h-4 w-4 mr-1" />
                  Assign Guests ({unassignedGuests.length})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Seating Chart Canvas */}
          <div
            ref={canvasRef}
            className="relative bg-gray-50 rounded-lg p-8 min-h-[500px] border-2 border-dashed border-gray-300 select-none"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
              <div className="bg-amber-100 px-4 py-2 rounded-lg text-center">
                <span className="font-medium text-amber-800">Main Stage</span>
              </div>
            </div>

            {/* Tables */}
            {tables.map((table) => {
              const occupiedSeats = table.guests.length
              const isFullyOccupied = occupiedSeats === table.seats
              const isEmpty = occupiedSeats === 0

              return (
                <div
                  key={table.id}
                  className="absolute cursor-move group"
                  style={{ left: table.x, top: table.y }}
                  onMouseDown={(e) => handleMouseDown(e, table.id)}
                  onClick={() => {
                    setSelectedTable(table)
                    setShowTableDetailsDialog(true)
                  }}
                >
                  <div className="relative">
                    <div className={`
                      w-20 h-20 bg-white border-2 flex items-center justify-center shadow-lg
                      group-hover:border-blue-500 transition-all duration-200 group-hover:scale-105
                      ${table.shape === 'round' ? 'rounded-full' : 'rounded-lg'}
                      ${isFullyOccupied ? 'border-green-400 bg-green-50' :
                        isEmpty ? 'border-gray-300' : 'border-amber-400 bg-amber-50'}
                      ${draggedTable === table.id ? 'shadow-2xl scale-105' : ''}
                    `}>
                      <div className="text-center">
                        <div className="font-bold text-gray-700 text-sm">{table.number}</div>
                        <div className="text-xs text-gray-500">{table.seats} seats</div>
                      </div>
                    </div>

                    <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                      <Badge
                        variant={isFullyOccupied ? "default" : isEmpty ? "secondary" : "outline"}
                        className={`text-xs ${
                          isFullyOccupied ? 'bg-green-100 text-green-800' :
                          isEmpty ? 'bg-gray-100 text-gray-600' : 'bg-amber-100 text-amber-800'
                        }`}
                      >
                        {occupiedSeats}/{table.seats}
                      </Badge>
                    </div>

                    {/* Table actions */}
                    <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-6 h-6 p-0 bg-white"
                          onClick={(e) => {
                            e.stopPropagation()
                            setSelectedTable(table)
                            setShowTableDetailsDialog(true)
                          }}
                        >
                          <Settings className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-6 h-6 p-0 bg-white text-red-600 hover:bg-red-50"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteTable(table.id)
                          }}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}

            {/* Legend */}
            <div className="absolute bottom-4 right-4 bg-white p-4 rounded-lg shadow-md">
              <h4 className="font-medium mb-2">Legend</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-green-100 border border-green-300 rounded"></div>
                  <span>Full Table</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-yellow-100 border border-yellow-300 rounded"></div>
                  <span>Partial</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                  <span>Empty</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table Details */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-600" />
            Table Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {tables.map((table) => {
              const occupiedSeats = table.guests.length
              const availableSeats = table.seats - occupiedSeats

              return (
                <div key={table.id} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">Table {table.number}</h4>
                    <Badge variant={occupiedSeats === table.seats ? "default" : "secondary"}>
                      {occupiedSeats}/{table.seats}
                    </Badge>
                  </div>
                  <div className="text-sm text-gray-600 mb-3">
                    <p>Capacity: {table.seats} guests</p>
                    <p>Occupied: {occupiedSeats} seats</p>
                    <p>Available: {availableSeats} seats</p>
                  </div>

                  {table.guests.length > 0 && (
                    <div className="mb-3">
                      <p className="text-xs font-medium text-gray-700 mb-1">Guests:</p>
                      <div className="space-y-1">
                        {table.guests.slice(0, 3).map((guest) => (
                          <div key={guest.id} className="text-xs text-gray-600 truncate">
                            {guest.name}
                          </div>
                        ))}
                        {table.guests.length > 3 && (
                          <div className="text-xs text-gray-500">
                            +{table.guests.length - 3} more...
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full bg-transparent"
                    onClick={() => {
                      setSelectedTable(table)
                      setShowTableDetailsDialog(true)
                    }}
                  >
                    <Edit className="w-3 h-3 mr-1" />
                    Manage Table
                  </Button>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Add Table Dialog */}
      <Dialog open={showAddTableDialog} onOpenChange={setShowAddTableDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Table</DialogTitle>
            <DialogDescription>
              Create a new table for your seating arrangement
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Table Number/Name *</label>
              <Input
                value={newTable.number}
                onChange={(e) => setNewTable({ ...newTable, number: e.target.value })}
                placeholder="e.g., Table 1, VIP Table, etc."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Number of Seats</label>
                <Input
                  type="number"
                  min="2"
                  max="20"
                  value={newTable.seats}
                  onChange={(e) => setNewTable({ ...newTable, seats: parseInt(e.target.value) || 8 })}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Table Shape</label>
                <Select
                  value={newTable.shape}
                  onValueChange={(value: 'round' | 'rectangle') => setNewTable({ ...newTable, shape: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="round">Round</SelectItem>
                    <SelectItem value="rectangle">Rectangle</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddTableDialog(false)}>
              Cancel
            </Button>
            <Button onClick={addTable}>
              Add Table
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Table Details Dialog */}
      <Dialog open={showTableDetailsDialog} onOpenChange={setShowTableDetailsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Table {selectedTable?.number} Details</DialogTitle>
            <DialogDescription>
              Manage guests and table settings
            </DialogDescription>
          </DialogHeader>

          {selectedTable && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Capacity: {selectedTable.seats} seats</p>
                  <p className="text-sm text-gray-600">Occupied: {selectedTable.guests.length} seats</p>
                  <p className="text-sm text-gray-600">Available: {selectedTable.seats - selectedTable.guests.length} seats</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Shape: {selectedTable.shape}</p>
                  <p className="text-sm text-gray-600">Position: ({Math.round(selectedTable.x)}, {Math.round(selectedTable.y)})</p>
                </div>
              </div>

              {selectedTable.guests.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Assigned Guests</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {selectedTable.guests.map((guest) => (
                      <div key={guest.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div>
                          <p className="font-medium text-sm">{guest.name}</p>
                          <p className="text-xs text-gray-600">{guest.email}</p>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => assignGuestToTable(guest.id, '')}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTableDetailsDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Guest Assignment Dialog */}
      <Dialog open={showGuestAssignDialog} onOpenChange={setShowGuestAssignDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Assign Unassigned Guests</DialogTitle>
            <DialogDescription>
              Assign guests who don't have a table to available tables
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 max-h-96 overflow-y-auto">
            {unassignedGuests.map((guest) => (
              <div key={guest.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">{guest.name}</p>
                  <p className="text-sm text-gray-600">{guest.email}</p>
                </div>
                <Select onValueChange={(tableNumber) => assignGuestToTable(guest.id, tableNumber)}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select table" />
                  </SelectTrigger>
                  <SelectContent>
                    {tables
                      .filter(table => table.guests.length < table.seats)
                      .map((table) => (
                        <SelectItem key={table.id} value={table.number}>
                          {table.number} ({table.seats - table.guests.length} available)
                        </SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowGuestAssignDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
