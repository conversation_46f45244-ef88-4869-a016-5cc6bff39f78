"use client"

import { useState, useEffect } from "react"
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2 } from "lucide-react"
import apiClient from "@/lib/api"
import { toast } from "sonner"

export function StatsChart() {
  const [chartData, setChartData] = useState([])
  const [chartType, setChartType] = useState("bar")
  const [timeRange, setTimeRange] = useState("6months")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadChartData()
  }, [timeRange])

  const loadChartData = async () => {
    try {
      setIsLoading(true)

      // Get analytics data from backend
      const analyticsData = await apiClient.getAnalytics(timeRange)

      // Transform the data for the chart
      const transformedData = analyticsData.map((item) => ({
        name: getMonthName(item.month, item.year),
        events: item.total_events || 0,
        guests: item.total_guests || 0,
        revenue: item.total_revenue || 0,
        vendors: item.active_vendors || 0,
      }))

      setChartData(transformedData)
    } catch (error) {
      console.error("Failed to load chart data:", error)

      // Fallback to sample data if backend fails
      const fallbackData = generateFallbackData()
      setChartData(fallbackData)

      toast.error("Failed to load analytics data, showing sample data")
    } finally {
      setIsLoading(false)
    }
  }

  const getMonthName = (month, year) => {
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    return `${monthNames[month - 1]} ${year}`
  }

  const generateFallbackData = () => {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]
    return months.map((month) => ({
      name: month,
      events: Math.floor(Math.random() * 10) + 3,
      guests: Math.floor(Math.random() * 500) + 200,
      revenue: Math.floor(Math.random() * 50000) + 10000,
      vendors: Math.floor(Math.random() * 20) + 5,
    }))
  }

  const getChartTitle = () => {
    switch (timeRange) {
      case "3months":
        return "Last 3 Months Analytics"
      case "6months":
        return "Last 6 Months Analytics"
      case "1year":
        return "Last 12 Months Analytics"
      default:
        return "Analytics Overview"
    }
  }

  if (isLoading) {
    return (
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle>Analytics</CardTitle>
          <CardDescription>Event and guest statistics over time</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px]">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading analytics...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{getChartTitle()}</CardTitle>
            <CardDescription>Event and guest statistics over time</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={chartType} onValueChange={setChartType}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">Bar Chart</SelectItem>
                <SelectItem value="line">Line Chart</SelectItem>
              </SelectContent>
            </Select>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-36">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3months">3 Months</SelectItem>
                <SelectItem value="6months">6 Months</SelectItem>
                <SelectItem value="1year">1 Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            {chartType === "bar" ? (
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => {
                    if (name === "revenue") {
                      return [`$${value.toLocaleString()}`, "Revenue"]
                    }
                    return [value, name.charAt(0).toUpperCase() + name.slice(1)]
                  }}
                />
                <Bar dataKey="events" fill="#8b5cf6" name="Events" />
                <Bar dataKey="guests" fill="#06b6d4" name="Guests" />
              </BarChart>
            ) : (
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => {
                    if (name === "revenue") {
                      return [`$${value.toLocaleString()}`, "Revenue"]
                    }
                    return [value, name.charAt(0).toUpperCase() + name.slice(1)]
                  }}
                />
                <Line type="monotone" dataKey="events" stroke="#8b5cf6" strokeWidth={2} name="Events" />
                <Line type="monotone" dataKey="guests" stroke="#06b6d4" strokeWidth={2} name="Guests" />
              </LineChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Chart Legend */}
        <div className="flex items-center justify-center gap-6 mt-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-purple-500 rounded"></div>
            <span>Events</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-cyan-500 rounded"></div>
            <span>Guests</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
