const mysql = require("mysql2/promise")
const fs = require("fs")
const path = require("path")
require("dotenv").config()

async function seedDatabase() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || "localhost",
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "otantik_ems",
    multipleStatements: true,
  })

  try {
    console.log("Seeding database with sample data...")

    const seedSQL = fs.readFileSync(path.join(__dirname, "../database/seed.sql"), "utf8")
    
    const statements = seedSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement + ';')
          console.log(`✅ Executed: ${statement.substring(0, 50)}...`)
        } catch (error) {
          console.error(`❌ Failed to execute: ${statement.substring(0, 50)}...`)
          throw error
        }
      }
    }

    console.log("✅ Database seeded successfully!")
    console.log("📧 Default admin login: <EMAIL>")
    console.log("🔑 Default password: password")
  } catch (error) {
    console.error("❌ Seeding failed:", error)
    process.exit(1)
  } finally {
    await connection.end()
  }
}

seedDatabase()
