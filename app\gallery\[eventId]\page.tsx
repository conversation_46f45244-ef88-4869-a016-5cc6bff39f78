"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Search, Download, Share2, Heart, Calendar, MapPin, Users } from "lucide-react"
import { toast } from "sonner"

interface EventPhoto {
  id: number
  original_name: string
  file_url: string
  caption: string
  file_size: number
  created_at: string
}

interface Event {
  id: number
  title: string
  event_date: string
  venue: string
  description: string
}

export default function EventGalleryPage({ params }: { params: { eventId: string } }) {
  const [event, setEvent] = useState<Event | null>(null)
  const [photos, setPhotos] = useState<EventPhoto[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedPhoto, setSelectedPhoto] = useState<EventPhoto | null>(null)

  useEffect(() => {
    loadEventGallery()
  }, [params.eventId])

  const loadEventGallery = async () => {
    try {
      setLoading(true)
      
      // Fetch event details
      const eventResponse = await fetch(`http://localhost:5000/api/events/${params.eventId}/public`)
      if (eventResponse.ok) {
        const eventData = await eventResponse.json()
        setEvent(eventData)
      }

      // Fetch event photos
      const photosResponse = await fetch(`http://localhost:5000/api/events/${params.eventId}/photos`)
      if (photosResponse.ok) {
        const photosData = await photosResponse.json()
        setPhotos(photosData)
      }
    } catch (error) {
      console.error("Failed to load event gallery:", error)
      toast.error("Failed to load event gallery")
    } finally {
      setLoading(false)
    }
  }

  const handleDownloadPhoto = async (photo: EventPhoto) => {
    try {
      const response = await fetch(photo.file_url)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = photo.original_name
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      toast.success("Photo downloaded successfully!")
    } catch (error) {
      console.error("Failed to download photo:", error)
      toast.error("Failed to download photo")
    }
  }

  const handleShareGallery = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href)
      toast.success("Gallery link copied to clipboard!")
    } catch (error) {
      console.error("Failed to copy gallery link:", error)
      toast.error("Failed to copy gallery link")
    }
  }

  const filteredPhotos = photos.filter(photo => {
    const search = searchTerm.toLowerCase()
    return photo.original_name.toLowerCase().includes(search) || 
           (photo.caption && photo.caption.toLowerCase().includes(search))
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading event gallery...</p>
        </div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Event Not Found</h1>
          <p className="text-gray-600">The event you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{event.title}</h1>
              <div className="flex items-center gap-4 mt-2 text-gray-600">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(event.event_date).toLocaleDateString()}</span>
                </div>
                {event.venue && (
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    <span>{event.venue}</span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button onClick={handleShareGallery} variant="outline">
                <Share2 className="h-4 w-4 mr-2" />
                Share Gallery
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="border-0 shadow-lg bg-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Photos</p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">{photos.length}</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Event Date</p>
                  <p className="text-3xl font-bold text-green-600 mt-2">
                    {new Date(event.event_date).toLocaleDateString()}
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Heart className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Venue</p>
                  <p className="text-lg font-semibold text-gray-900 mt-2">{event.venue || "TBD"}</p>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <MapPin className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card className="border-0 shadow-lg mb-8">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search photos by name or caption..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Photos Grid */}
        {filteredPhotos.length === 0 ? (
          <Card className="border-0 shadow-lg">
            <CardContent className="p-12 text-center">
              <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No photos found</h3>
              <p className="text-gray-600">
                {searchTerm ? "No photos match your search." : "No photos have been uploaded yet."}
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredPhotos.map((photo) => (
              <Card key={photo.id} className="border-0 shadow-lg bg-white hover:shadow-xl transition-shadow cursor-pointer">
                <CardContent className="p-0">
                  <div className="relative group">
                    <img
                      src={`http://localhost:5000${photo.file_path}`}
                      alt={photo.original_name}
                      className="w-full h-64 object-cover rounded-t-lg"
                      onClick={() => setSelectedPhoto(photo)}
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <Button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDownloadPhoto(photo)
                          }}
                          size="sm"
                          className="bg-white text-gray-900 hover:bg-gray-100"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-gray-900 truncate">{photo.original_name}</h3>
                    {photo.caption && (
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">{photo.caption}</p>
                    )}
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500">
                        {photo.file_size ? (photo.file_size / 1024 / 1024).toFixed(1) + " MB" : "Unknown size"}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        Photo
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Photo Modal */}
      {selectedPhoto && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="relative">
              <img
                src={selectedPhoto.file_url}
                alt={selectedPhoto.original_name}
                className="w-full h-auto max-h-[70vh] object-contain"
              />
              <Button
                onClick={() => setSelectedPhoto(null)}
                variant="ghost"
                size="sm"
                className="absolute top-4 right-4 bg-white bg-opacity-75 hover:bg-opacity-100"
              >
                ×
              </Button>
            </div>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900">{selectedPhoto.original_name}</h3>
              {selectedPhoto.caption && (
                <p className="text-gray-600 mt-2">{selectedPhoto.caption}</p>
              )}
              <div className="flex items-center justify-between mt-4">
                <span className="text-sm text-gray-500">
                  {selectedPhoto.file_size ? (selectedPhoto.file_size / 1024 / 1024).toFixed(1) + " MB" : "Unknown size"}
                </span>
                <Button onClick={() => handleDownloadPhoto(selectedPhoto)}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 
