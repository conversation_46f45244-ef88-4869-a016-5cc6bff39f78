import apiClient from "@/lib/api"

export interface Event {
  id: number
  title: string
  description: string
  event_date: string
  event_time: string
  venue: string
  address: string
  region: string
  expected_guests: number
  budget: number
  planner_id: number
  status: "planning" | "confirmed" | "completed" | "cancelled"
  invitation_template: string
  created_at: string
  updated_at: string
}

export interface CreateEventData {
  title: string
  description: string
  event_date: string
  event_time: string
  venue: string
  address: string
  region: string
  expected_guests: number
  budget: number
}

class EventService {
  async getEvents(): Promise<Event[]> {
    return apiClient.request("/events")
  }

  async getEvent(id: number): Promise<Event> {
    return apiClient.request(`/events/${id}`)
  }

  async createEvent(eventData: CreateEventData): Promise<Event> {
    return apiClient.request("/events", {
      method: "POST",
      body: eventData,
    })
  }

  async updateEvent(id: number, eventData: Partial<Event>): Promise<Event> {
    return apiClient.request(`/events/${id}`, {
      method: "PUT",
      body: eventData,
    })
  }

  async deleteEvent(id: number): Promise<void> {
    return apiClient.request(`/events/${id}`, {
      method: "DELETE",
    })
  }

  async sendInvitations(eventId: number, message: string, imageUrl?: string): Promise<any> {
    return apiClient.request(`/events/${eventId}/send-invitations`, {
      method: "POST",
      body: { message, invitationImageUrl: imageUrl },
    })
  }

  async sendThankYouMessages(eventId: number, message: string): Promise<any> {
    return apiClient.request(`/events/${eventId}/send-thank-you`, {
      method: "POST",
      body: { message },
    })
  }
}

export const eventService = new EventService()
