const mysql = require("mysql2/promise")

// Test different connection configurations
const configs = [
  {
    name: "Default (root, no password)",
    host: "localhost",
    user: "root",
    password: "",
    database: "otantik_ems"
  },
  {
    name: "Root with password",
    host: "localhost", 
    user: "root",
    password: "root",
    database: "otantik_ems"
  },
  {
    name: "Root with admin password",
    host: "localhost",
    user: "root", 
    password: "admin",
    database: "otantik_ems"
  }
]

async function testConnections() {
  console.log("🔍 Testing MySQL connections...\n")

  for (const config of configs) {
    try {
      console.log(`Testing: ${config.name}`)
      
      // First try to connect without database
      const connection = await mysql.createConnection({
        host: config.host,
        user: config.user,
        password: config.password
      })

      console.log(`✅ Connected to MySQL server successfully!`)
      
      // Check if database exists
      const [databases] = await connection.execute("SHOW DATABASES")
      const dbExists = databases.some(db => db.Database === config.database)
      
      if (dbExists) {
        console.log(`✅ Database '${config.database}' exists`)
        
        // Try to connect to the specific database
        await connection.end()
        const dbConnection = await mysql.createConnection(config)
        console.log(`✅ Successfully connected to database '${config.database}'`)
        await dbConnection.end()
        
        console.log(`\n🎉 SUCCESS! Use this configuration:`)
        console.log(`Host: ${config.host}`)
        console.log(`User: ${config.user}`)
        console.log(`Password: ${config.password || '(empty)'}`)
        console.log(`Database: ${config.database}`)
        console.log(`\n`)
        
        return config
      } else {
        console.log(`❌ Database '${config.database}' does not exist`)
        console.log(`Available databases:`)
        databases.forEach(db => console.log(`  - ${db.Database}`))
      }
      
      await connection.end()
      
    } catch (error) {
      console.log(`❌ Failed: ${error.message}`)
    }
    
    console.log("---")
  }
  
  console.log("❌ No working configuration found.")
  console.log("\n💡 Please check:")
  console.log("1. Is MySQL running? (XAMPP/WAMP should be started)")
  console.log("2. What is your MySQL root password?")
  console.log("3. What is the exact name of your database?")
}

testConnections() 